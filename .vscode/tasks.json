{"version": "2.0.0", "tasks": [{"label": "start-dependencies", "type": "shell", "command": "docker", "args": ["compose", "-f", "backend/docker-compose.local.yml", "up", "-d", "db", "localstack", "localstack-init", "ftp"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "stop-dependencies", "type": "shell", "command": "docker", "args": ["compose", "-f", "backend/docker-compose.local.yml", "down"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "install-dependencies", "type": "shell", "command": "poetry", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}/backend"}, "problemMatcher": []}, {"label": "run-migrations", "type": "shell", "command": "poetry", "args": ["run", "alembic", "upgrade", "head"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}/backend"}, "problemMatcher": []}, {"label": "seed-database", "type": "shell", "command": "poetry", "args": ["run", "seed", "--sowise-dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}/backend"}, "problemMatcher": []}, {"label": "setup-dev-environment", "dependsOrder": "sequence", "dependsOn": ["start-dependencies", "install-dependencies", "run-migrations", "seed-database"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}