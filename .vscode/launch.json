{"version": "0.2.0", "configurations": [{"name": "Debug SP App", "type": "python", "request": "launch", "program": "${workspaceFolder}/backend/dev_run.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/backend", "env": {"PYTHONPATH": "${workspaceFolder}/backend", "DB_HOSTNAME": "localhost", "DB_PORT": "4407", "DB_USERNAME": "dev", "DB_PASSWORD": "dev", "DB_NAME": "dev_db"}, "envFile": "${workspaceFolder}/backend/.env", "justMyCode": false, "stopOnEntry": false, "preLaunchTask": "start-dependencies"}, {"name": "Debug SP App (Simple)", "type": "python", "request": "launch", "program": "${workspaceFolder}/backend/dev_run.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/backend", "env": {"PYTHONPATH": "${workspaceFolder}/backend", "DB_HOSTNAME": "localhost", "DB_PORT": "4407"}, "envFile": "${workspaceFolder}/backend/.env", "justMyCode": false, "stopOnEntry": false}, {"name": "Debug SP App with Gunicorn", "type": "python", "request": "launch", "module": "gunicorn", "args": ["--bind", "0.0.0.0:6543", "--reload", "--timeout", "120", "--workers", "1", "--threads", "1", "strandproperties.wsgi:application"], "console": "integratedTerminal", "cwd": "${workspaceFolder}/backend", "env": {"PYTHONPATH": "${workspaceFolder}/backend", "DB_HOSTNAME": "localhost", "DB_PORT": "4407"}, "envFile": "${workspaceFolder}/backend/.env", "justMyCode": false, "stopOnEntry": false, "preLaunchTask": "start-dependencies"}]}