{"python.defaultInterpreterPath": "./backend/.venv/bin/python", "python.terminal.activateEnvironment": true, "python.analysis.extraPaths": ["./backend"], "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["./backend/strandproperties/tests"], "python.testing.cwd": "./backend", "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "python.linting.enabled": true, "python.formatting.provider": "black", "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "editor.formatOnSave": true, "python.sortImports.args": ["--profile", "black"]}