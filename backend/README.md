# Strand Properties

## Prerequisites

* Python 3.11+
* Poetry 2.1+

* For local development macOS install WeasyPrint dependencies
```sh
brew install pango cairo gdk-pixbuf libffi
```

## Setup for development

```sh
docker compose -f docker-compose.local.yml up -d
poetry install
poetry run alembic upgrade head
poetry run seed --sowise-dev
./scripts/run.dev.sh
```

Then app will be available at: `http://0.0.0.0:6543`

API explorer will be available at: `http://0.0.0.0:6543/-/spec`

We are now using the AD auth integration so to be able to login you need to create a user
with your AD authenticated email

```sh
poetry <NAME_EMAIL> pwd --first-name=John --last-name=Doe --role=Admin --organization=1
```

## Database migration

If you need to generate new DB revision automatically:

```sh
poetry run alembic revision --autogenerate -m 'Comment'
```

Make sure to apply this revision locally and test the changes

## Task queue

The project is using 2 task queue manager:

### SQS

This utilizes [AWS SQS](https://aws.amazon.com/sqs/) to manage the task queue. 
Currently this is used for all portal integrations and Facebook lead processing.

To run the listener:

```sh
poetry run strandproperties/libs/sqs_listener.py
```

Note: on AWS, this is currently run as a supervisor process, check [supervisord.conf](./supervisord.conf) for more details.

### Huey

This utilizes [Huey](https://huey.readthedocs.io/en/latest/) and Redis to manage the task queue.
This offers more flexibility for adding tasks to the queue, and spreading the load across multiple workers.
It's also easier to develop and test locally since it doesn't depend on any AWS services.

To run the worker process:

```sh
poetry run worker
```

## Automation tests

Create test DB:

```sh
docker-compose exec -it db mysql -uroot -proot -e "GRANT ALL ON *.* TO dev@'%';"
docker-compose exec -it db mysql -udev -pdev -e "CREATE DATABASE IF NOT EXISTS test_db;"
```

Then run the test suite with:

```sh
poetry run pytest
```

If you want to run the unit tests without db initialization you can use skip-db-setup flag

```sh
poetry run pytest --skip-db-setup -k "test_name"
```

## Setup pre-commit hook

This repository uses [pre-commit](https://pre-commit.com/) to enforce code style and formatting. To install the pre-commit hook, run:

```sh
pre-commit install
```

## Misc

* Connect to dev DB: `docker-compose exec -it db mysql -udev -pdev dev_db`
* Restore from dump: `zcat data/dump_20230603.sql.gz | docker-compose exec -T db mysql -udev -pdev dev_db`
* Create an organization: `poetry run organization <random_organization_name>`
* Create a test realtor user: `poetry <NAME_EMAIL> pwd --role=Realtor --organization=1`
* Create a test contact contact: `poetry <NAME_EMAIL> --first-name=contact --last-name=test`
* To execute any sql script outside of docker: `cat <sql_script_path>.sql | docker-compose exec -T db mysql -udev -pdev dev_db`

## Sowise integration flow

1. Organization flow:
    * Use script to initiate Sowise integration: `poetry run organization "Strand Spain" --sowise-email=<EMAIL>`
    * This will create a Sowise account, then go to https://app-test.sowise.fi/ with same email address to start signing up, and later on accept integration
    * Everytime the owner user accepts integration, an API key will be sent to Strand webhook `/webhook/sowise` and saved to Strand DB under organization details
    * Then trigger above script again to create a Sowise organization

2. Use `SowiseClient`:

```python
from strand.libs.sowise import SowiseClient

org = db_session.query(Organization).filter_by(id=1).first()

c = SowiseClient(
    access_key=app_cfg.sowise_access_key,
    base_url=app_cfg.sowise_base_url,
    api_key=org.details["sowise_api_key"],
)

... do something with the client
```

