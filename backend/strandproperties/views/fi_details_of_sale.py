from pyramid.view import view_config
from pyramid.exceptions import HTTPNotFound, HTTPBadRequest
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload
from strandproperties.libs.utils import update_model_from_schema

from strandproperties.logger import logger
from strandproperties.models.fi_details_of_sale import (
    FIDetailsOfSale,
    FISellerDetailsOfSale,
    FIBuyerDetailsOfSale,
    FIRecipientDetailsOfSale,
)
from strandproperties.models.fi_sales_agreement import FISalesAgreement
from strandproperties.models.fi_purchase_offer import FIPurchaseOffer
from strandproperties.constants import (
    FISalesAgreementCommissionTypeEnum,
    FISalesAgreementStatusEnum,
    FIPurchaseOfferStatusEnum,
)
from strandproperties.schemas.fi_details_of_sale import (
    FIDetailsOfSaleCreate,
    FIDetailsOfSaleRead,
    FIDetailsOfSaleEdit,
    FIDetailsOfSaleStatus,
    FIDetailsOfSaleStatusUpdate,
    FIDetailsOfSalePrefillRead,
    validate_complete_schema,
)
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError
from strandproperties.schemas.param import IdParam, PropertyIdParam, ValidateParam


class FIDetailsOfSaleViews(BaseApi):
    @view_config(
        route_name="fi_details_of_sale.prefill",
        request_method="GET",
        permission="fi_details_of_sale:read",
        openapi_param_schema=PropertyIdParam,
        openapi_response_schema=FIDetailsOfSalePrefillRead,
        openapi_metadata={
            "tags": ["FI Details of Sale"],
            "summary": "Get prefill data for FI Details of Sale",
            "description": "Prefills fields from latest Sales Agreement and Purchase Offer for a property.",
        },
    )
    def get_fi_details_of_sale_prefill(self):
        params = PropertyIdParam(**self.request.matchdict)

        latest_completed_sales_agreement = self.db_session.scalars(
            select(FISalesAgreement)
            .where(
                (FISalesAgreement.property_id == params.property_id)
                & (FISalesAgreement.status == FISalesAgreementStatusEnum.COMPLETED)
            )
            .options(joinedload(FISalesAgreement.contacts))
            .order_by(FISalesAgreement.created_at.desc())
        ).first()

        latest_accepted_purchase_offer = self.db_session.scalars(
            select(FIPurchaseOffer)
            .where(
                (FIPurchaseOffer.property_id == params.property_id)
                & (FIPurchaseOffer.status == FIPurchaseOfferStatusEnum.ACCEPTED)
            )
            .options(joinedload(FIPurchaseOffer.buyers))
            .order_by(FIPurchaseOffer.created_at.desc())
        ).first()

        sellers_ids = (
            [c.id for c in latest_completed_sales_agreement.contacts]
            if latest_completed_sales_agreement
            else None
        )
        buyers_ids = (
            [c.id for c in latest_accepted_purchase_offer.buyers]
            if latest_accepted_purchase_offer
            else None
        )

        commission_amount_total = None
        commission_percent = None
        if (
            latest_completed_sales_agreement
            and latest_completed_sales_agreement.commission_type
        ):
            if (
                latest_completed_sales_agreement.commission_type
                == FISalesAgreementCommissionTypeEnum.FIXED
            ):
                commission_amount_total = (
                    latest_completed_sales_agreement.commission_fixed
                )
            elif (
                latest_completed_sales_agreement.commission_type
                == FISalesAgreementCommissionTypeEnum.PERCENTAGE
            ):
                commission_percent = (
                    latest_completed_sales_agreement.commission_percentage
                )

        prefill = FIDetailsOfSalePrefillRead(
            assignment_started_at=(
                latest_completed_sales_agreement.start_date
                if latest_completed_sales_agreement
                else None
            ),
            sale_duration_days=None,
            sale_price=(
                latest_accepted_purchase_offer.unencumbered_price
                if latest_accepted_purchase_offer
                else None
            ),
            commission_amount_total=commission_amount_total,
            commission_percent=commission_percent,
            sellers=sellers_ids,
            buyers=buyers_ids,
        )

        return prefill

    @view_config(
        route_name="fi_details_of_sale.list_create",
        request_method="GET",
        permission="fi_details_of_sale:read",
        openapi_param_schema=PropertyIdParam,
        openapi_response_schema=FIDetailsOfSaleRead,
        openapi_metadata={
            "tags": ["FI Details of Sale"],
        },
    )
    def list_fi_details_of_sale(self):
        try:
            params = PropertyIdParam(**self.request.GET)
            stmt = select(FIDetailsOfSale).where(
                FIDetailsOfSale.property_id == params.property_id
            )
            result = self.db_session.execute(stmt).scalars().all()

            return [FIDetailsOfSaleRead.from_orm(row) for row in result]
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not list FI details of sale")

    @view_config(
        route_name="fi_details_of_sale.list_create",
        request_method="POST",
        permission="fi_details_of_sale:create",
        openapi_param_schema=ValidateParam,
        openapi_request_schema=FIDetailsOfSaleCreate,
        openapi_response_schema=FIDetailsOfSaleRead,
        openapi_metadata={
            "tags": ["FI Details of Sale"],
        },
    )
    def create_fi_details_of_sale(self):
        try:
            validate_param = ValidateParam(**self.request.params)
            should_validate = validate_param.should_validate

            payload = {
                **self.request.json_body,
                "created_by": self.user_id,
            }

            create_schema = FIDetailsOfSaleCreate(**payload)

            fi_details_of_sale = self.db_session.scalars(
                select(FIDetailsOfSale).where(
                    FIDetailsOfSale.property_id == create_schema.property_id
                )
            ).one_or_none()

            if fi_details_of_sale:
                raise HTTPBadRequest(
                    "FI details of sale already exists, can only have one",
                )

            details_of_sale_data = create_schema.model_dump()

            sellers_data = details_of_sale_data.pop("sellers", [])
            buyers_data = details_of_sale_data.pop("buyers", [])
            recipients_data = details_of_sale_data.pop("recipients", [])

            # Create the main FIDetailsOfSale instance
            details_of_sale = FIDetailsOfSale(**details_of_sale_data)

            details_of_sale.status = FIDetailsOfSaleStatus.DRAFT
            if should_validate is True:
                validate_complete_schema(create_schema)
                details_of_sale.status = FIDetailsOfSaleStatus.VALIDATED

            # Handle sellers
            details_of_sale.sellers = [
                FISellerDetailsOfSale(**seller_data) for seller_data in sellers_data
            ]
            # Handle buyers
            details_of_sale.buyers = [
                FIBuyerDetailsOfSale(**buyer_data) for buyer_data in buyers_data
            ]

            # Handle recipients
            details_of_sale.recipients = [
                FIRecipientDetailsOfSale(**recipient_data)
                for recipient_data in recipients_data
            ]

            self.db_session.add(details_of_sale)
            self.db_session.flush()

        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not create FI details of sale")

        self.request.response.status = 201
        return FIDetailsOfSaleRead.model_validate(details_of_sale)

    @view_config(
        route_name="fi_details_of_sale.read_edit",
        request_method="GET",
        permission="fi_details_of_sale:read",
        openapi_param_schema=IdParam,
        openapi_response_schema=FIDetailsOfSaleRead,
        openapi_metadata={
            "tags": ["FI Details of Sale"],
        },
    )
    def read_fi_details_of_sale(self):
        params = IdParam(**self.request.matchdict)
        try:
            fi_details_of_sale = self.db_session.scalars(
                select(FIDetailsOfSale).where(FIDetailsOfSale.id == params.id)
            ).one()
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise HTTPNotFound("Could not read FI details of sale")

        return FIDetailsOfSaleRead.model_validate(fi_details_of_sale)

    @view_config(
        route_name="fi_details_of_sale.read_edit",
        request_method="PATCH",
        permission="fi_details_of_sale:update",
        openapi_param_schema=ValidateParam,
        openapi_request_schema=FIDetailsOfSaleEdit,
        openapi_response_schema=FIDetailsOfSaleRead,
        openapi_metadata={
            "tags": ["FI Details of Sale"],
        },
    )
    def edit_fi_details_of_sale(self):
        fi_details_of_sale_id = self.request.matchdict["id"]
        validate_param = ValidateParam(**self.request.params)
        should_validate = validate_param.should_validate

        fi_details_of_sale = self.db_session.scalars(
            select(FIDetailsOfSale).where(FIDetailsOfSale.id == fi_details_of_sale_id)
        ).one_or_none()

        if not fi_details_of_sale:
            raise HTTPNotFound("Could not read FI details of sale")

        if fi_details_of_sale.status == FIDetailsOfSaleStatus.LOCKED:
            raise HTTPBadRequest("Could not edit FI details of sale its already locked")

        if not self.is_admin and self.user_id != fi_details_of_sale.created_by:
            raise ApiError("Unauthorized to edit FI details of sale", status_code=403)

        edit_schema = FIDetailsOfSaleEdit(**self.request.json_body)

        edit_schema.status = FIDetailsOfSaleStatus.DRAFT
        if should_validate is True:
            validate_complete_schema(edit_schema)
            edit_schema.status = FIDetailsOfSaleStatus.VALIDATED

        update_model_from_schema(
            model_obj=fi_details_of_sale,
            schema_obj=edit_schema,
            exclude=["id", "created_by", "sellers", "buyers", "recipients"],
        )

        edit_schema_data = edit_schema.model_dump()

        update_sellers(
            self.db_session, fi_details_of_sale.id, edit_schema_data.pop("sellers", [])
        )
        update_buyers(
            self.db_session, fi_details_of_sale.id, edit_schema_data.pop("buyers", [])
        )
        update_recipients(
            self.db_session,
            fi_details_of_sale.id,
            edit_schema_data.pop("recipients", []),
        )

        self.db_session.flush()
        self.request.response.status = 200
        return FIDetailsOfSaleRead.model_validate(fi_details_of_sale)

    @view_config(
        route_name="fi_details_of_sale.update_status",
        request_method="PATCH",
        permission="fi_details_of_sale:update_status",
        openapi_param_schema=IdParam,
        openapi_request_schema=FIDetailsOfSaleStatusUpdate,
        openapi_response_schema=FIDetailsOfSaleRead,
        openapi_metadata={
            "tags": ["FI Details of Sale"],
            "summary": "Update the status of an FI Details of Sale record",
        },
    )
    def update_status_fi_details_of_sale(self):
        fi_details_of_sale_id = self.request.matchdict["id"]

        fi_details_of_sale = self.db_session.scalars(
            select(FIDetailsOfSale).where(FIDetailsOfSale.id == fi_details_of_sale_id)
        ).one_or_none()

        if not fi_details_of_sale:
            raise HTTPNotFound("FI Details of Sale record not found")

        if not self.is_admin and self.user_id != fi_details_of_sale.created_by:
            raise ApiError(
                "Unauthorized to update status for this FI Details of Sale record",
                status_code=403,
            )

        if fi_details_of_sale.status == FIDetailsOfSaleStatus.DRAFT:
            raise HTTPBadRequest(
                "Could not edit FI details of sale status since its still a draft"
            )

        try:
            status_update_schema = FIDetailsOfSaleStatusUpdate(**self.request.json_body)
            fi_details_of_sale.status = status_update_schema.status
            self.db_session.flush()
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not update status for FI Details of Sale record")
        except Exception as e:
            logger.error(
                f"Error validating status update payload: {e}",
                extra={"requestId": self.request.request_id},
            )
            raise ApiError(f"Invalid status update payload: {e}", status_code=400)

        self.request.response.status = 200
        return FIDetailsOfSaleRead.model_validate(fi_details_of_sale)


def update_sellers(db_session, fi_details_of_sale_id, sellers_data):
    db_session.query(FISellerDetailsOfSale).filter_by(
        fi_details_of_sale_id=fi_details_of_sale_id
    ).delete()
    for seller_data in sellers_data:
        seller = FISellerDetailsOfSale(
            fi_details_of_sale_id=fi_details_of_sale_id, **seller_data
        )
        db_session.add(seller)


def update_buyers(db_session, fi_details_of_sale_id, buyers_data):
    db_session.query(FIBuyerDetailsOfSale).filter_by(
        fi_details_of_sale_id=fi_details_of_sale_id
    ).delete()
    for buyer_data in buyers_data:
        buyer = FIBuyerDetailsOfSale(
            fi_details_of_sale_id=fi_details_of_sale_id, **buyer_data
        )
        db_session.add(buyer)


def update_recipients(db_session, fi_details_of_sale_id, recipients_data):
    db_session.query(FIRecipientDetailsOfSale).filter_by(
        fi_details_of_sale_id=fi_details_of_sale_id
    ).delete()
    for recipient_data in recipients_data:
        recipient = FIRecipientDetailsOfSale(
            fi_details_of_sale_id=fi_details_of_sale_id, **recipient_data
        )
        db_session.add(recipient)
