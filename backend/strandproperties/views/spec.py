from string import Template

import pkg_resources
from apispec import APISpec
from apispec_pydantic_plugin import PydanticPlugin
from pyramid.request import Request
from pyramid.view import view_config
from pyramid_apispec.helpers import add_pyramid_paths

from strandproperties.config import app_cfg


@view_config(
    route_name="openapi_spec", renderer="json", permission="__no_permission_required__"
)
def api_spec(request):
    """View to generate the OpenAPI JSON output."""
    spec = APISpec(
        title="Strand API",
        version="1.0.0",
        openapi_version="3.0.2",
        plugins=[PydanticPlugin()],
    )

    bearer_auth_scheme = {
        "type": "http",
        "scheme": "bearer",
        "bearerFormat": "JWT",
    }

    api_key_auth_scheme = {
        "type": "apiKey",
        "in": "header",
        "name": "X-API-KEY",
    }
    spec.components.security_scheme("BearerAuth", bearer_auth_scheme)
    spec.components.security_scheme("ApiKeyAuth", api_key_auth_scheme)

    add_pyramid_paths(spec, "auth.login", request=request)
    add_pyramid_paths(spec, "auth.reset_password.generate", request=request)
    add_pyramid_paths(spec, "auth.reset_password", request=request)
    add_pyramid_paths(spec, "user.me", request=request)
    add_pyramid_paths(spec, "user.list_create", request=request)
    add_pyramid_paths(spec, "user.change_preview_mode", request=request)
    add_pyramid_paths(spec, "user.read_edit", request=request)
    add_pyramid_paths(spec, "user.activate", request=request)
    add_pyramid_paths(spec, "user.deactivate", request=request)
    add_pyramid_paths(spec, "user.change_role", request=request)
    add_pyramid_paths(spec, "property.list_create", request=request)
    add_pyramid_paths(spec, "property.read_edit", request=request)
    add_pyramid_paths(spec, "property.get_keyword_suggestions", request=request)
    add_pyramid_paths(spec, "property.validation", request=request)
    add_pyramid_paths(spec, "property.archive", request=request)
    add_pyramid_paths(spec, "image.crud", request=request)
    add_pyramid_paths(spec, "image.generate_presigned_urls", request=request)
    add_pyramid_paths(spec, "image.download_zip", request=request)
    add_pyramid_paths(spec, "file.crud", request=request)
    add_pyramid_paths(spec, "file.generate_presigned_urls", request=request)
    add_pyramid_paths(spec, "file.get", request=request)
    add_pyramid_paths(spec, "mapping.list_features", request=request)
    add_pyramid_paths(spec, "mapping.list_cities", request=request)
    add_pyramid_paths(spec, "mapping.list_sub_areas", request=request)
    add_pyramid_paths(spec, "mapping.list_conditions", request=request)
    add_pyramid_paths(spec, "mapping.list_settings", request=request)
    add_pyramid_paths(spec, "mapping.list_views", request=request)
    add_pyramid_paths(spec, "mapping.list_orientations", request=request)
    add_pyramid_paths(spec, "mapping.list_property_types", request=request)
    add_pyramid_paths(spec, "mapping.list_nested_areas", request=request)
    add_pyramid_paths(spec, "mapping.list_all_nested_areas", request=request)
    add_pyramid_paths(spec, "mapping.read_nested_area", request=request)
    add_pyramid_paths(spec, "mapping.list_listing_types", request=request)
    add_pyramid_paths(spec, "mapping.list_garage_types", request=request)
    add_pyramid_paths(spec, "mapping.list_pool_types", request=request)
    add_pyramid_paths(spec, "mapping.list_garden_types", request=request)
    add_pyramid_paths(spec, "mapping.list_fi_property_types", request=request)
    add_pyramid_paths(spec, "mapping.list_banks", request=request)
    add_pyramid_paths(spec, "contact.list_create", request=request)
    add_pyramid_paths(spec, "contact.read_edit", request=request)
    add_pyramid_paths(spec, "contact.assign_multiple_contacts", request=request)
    add_pyramid_paths(spec, "nationality.list", request=request)
    add_pyramid_paths(spec, "event_logs.activity", request=request)
    add_pyramid_paths(spec, "event_logs.custom", request=request)
    add_pyramid_paths(spec, "offer.create", request=request)
    add_pyramid_paths(spec, "offer.remove", request=request)
    add_pyramid_paths(spec, "offer.list", request=request)
    add_pyramid_paths(spec, "offer.reject", request=request)
    add_pyramid_paths(spec, "offer.document_info", request=request)
    add_pyramid_paths(spec, "damage.upload_attachments", request=request)
    add_pyramid_paths(spec, "details_of_sale.create", request=request)
    add_pyramid_paths(spec, "details_of_sale.upload_attachments", request=request)
    add_pyramid_paths(spec, "details_of_sale.remove_attachments", request=request)
    add_pyramid_paths(spec, "details_of_sale.rename_attachments", request=request)
    add_pyramid_paths(spec, "details_of_sale.list", request=request)
    add_pyramid_paths(spec, "details_of_sale.retrieve", request=request)
    add_pyramid_paths(spec, "details_of_sale.status", request=request)
    add_pyramid_paths(spec, "details_of_sale.request_changes", request=request)
    add_pyramid_paths(spec, "details_of_sale.download", request=request)
    add_pyramid_paths(spec, "details_of_sale.transaction", request=request)
    add_pyramid_paths(spec, "details_of_sale.report_kpi", request=request)
    add_pyramid_paths(spec, "sales_agreement.document_info", request=request)

    add_pyramid_paths(spec, "sowise.create", request=request)
    add_pyramid_paths(spec, "sowise.create_sales_agreement", request=request)
    add_pyramid_paths(spec, "sowise.upload_signed_sales_agreement", request=request)
    add_pyramid_paths(spec, "sowise.fill", request=request)
    add_pyramid_paths(spec, "sowise.download", request=request)
    add_pyramid_paths(spec, "sowise.content", request=request)
    add_pyramid_paths(spec, "sowise.delete", request=request)
    add_pyramid_paths(spec, "sowise.add_signee", request=request)
    add_pyramid_paths(spec, "sowise.check_signing_status", request=request)
    add_pyramid_paths(spec, "sowise.stats", request=request)
    add_pyramid_paths(spec, "sowise.upload", request=request)

    add_pyramid_paths(spec, "public.es_property.list", request=request)
    add_pyramid_paths(spec, "public.es_property.read", request=request)
    add_pyramid_paths(spec, "public.realtor.list", request=request)
    add_pyramid_paths(spec, "public.realtor.read", request=request)
    add_pyramid_paths(spec, "public.es_area.list", request=request)

    add_pyramid_paths(spec, "integrations.rightmove.post_property", request=request)
    add_pyramid_paths(spec, "integrations.rightmove.remove_property", request=request)
    add_pyramid_paths(spec, "integrations.rightmove.get_properties", request=request)

    add_pyramid_paths(
        spec, "integrations.inmobilienscout24.post_property", request=request
    )
    add_pyramid_paths(
        spec, "integrations.inmobilienscout24.read_property", request=request
    )
    add_pyramid_paths(
        spec, "integrations.inmobilienscout24.update_property", request=request
    )
    add_pyramid_paths(
        spec, "integrations.inmobilienscout24.remove_property", request=request
    )

    add_pyramid_paths(spec, "integrations.fotocasa.post_property", request=request)
    add_pyramid_paths(spec, "integrations.fotocasa.update_property", request=request)
    add_pyramid_paths(spec, "integrations.fotocasa.remove_property", request=request)

    add_pyramid_paths(spec, "tag.list_create", request=request)

    add_pyramid_paths(spec, "lead.list_create", request=request)
    add_pyramid_paths(spec, "lead.list_pagination", request=request)
    add_pyramid_paths(spec, "lead.read_edit", request=request)

    add_pyramid_paths(spec, "match_making.list_create", request=request)
    add_pyramid_paths(spec, "match_making.read_edit", request=request)
    add_pyramid_paths(spec, "match_making.contact.delete", request=request)
    add_pyramid_paths(spec, "match_making.contact.email.delete", request=request)
    add_pyramid_paths(spec, "match_making.property.list_read", request=request)
    add_pyramid_paths(spec, "match_making.ms.email_list", request=request)
    add_pyramid_paths(spec, "match_making.ms.email_create", request=request)
    add_pyramid_paths(spec, "match_making.property.edit", request=request)
    add_pyramid_paths(spec, "match_making.mailgun.email_insights", request=request)
    add_pyramid_paths(spec, "match_making.mailgun.email_template.list", request=request)
    add_pyramid_paths(spec, "match_making.track.create_edit", request=request)
    add_pyramid_paths(spec, "match_making.read_edit.email_auto_sent", request=request)
    add_pyramid_paths(spec, "match_making.newsletter_subscribe", request=request)

    add_pyramid_paths(spec, "event.list_create", request=request)
    add_pyramid_paths(spec, "event.read_edit", request=request)

    add_pyramid_paths(spec, "dias.list_create_kiinteistokauppa", request=request)
    add_pyramid_paths(spec, "dias.list_create_vuokraoikeuden_siirto", request=request)
    add_pyramid_paths(spec, "dias.read_edit", request=request)
    add_pyramid_paths(spec, "dias.list_create_shared_trade", request=request)
    add_pyramid_paths(spec, "dias.read_edit_shared_trade", request=request)
    add_pyramid_paths(spec, "dias.initiate_shared_trade", request=request)
    add_pyramid_paths(spec, "dias.list_banks", request=request)
    add_pyramid_paths(spec, "dias.config", request=request)
    add_pyramid_paths(spec, "dias.start_signing_shared_trade", request=request)
    add_pyramid_paths(spec, "dias.cancel_shared_trade", request=request)
    add_pyramid_paths(spec, "dias.list_create_attachments", request=request)
    add_pyramid_paths(spec, "dias.generate_presigned_attachment_urls", request=request)
    add_pyramid_paths(spec, "dias.read_attachment", request=request)
    add_pyramid_paths(spec, "manual_assigned_lead.list", request=request)
    add_pyramid_paths(spec, "manual_assigned_lead.allocate", request=request)
    add_pyramid_paths(spec, "manual_assigned_lead.reallocate", request=request)
    add_pyramid_paths(spec, "manual_assigned_lead.toggle_discarded", request=request)

    add_pyramid_paths(spec, "fi_property.list_create", request=request)
    add_pyramid_paths(spec, "fi_property.read_edit", request=request)
    add_pyramid_paths(spec, "fi_property.duplicate", request=request)
    add_pyramid_paths(spec, "fi_property.import_from_kivi", request=request)
    add_pyramid_paths(spec, "fi_property_type.list", request=request)
    add_pyramid_paths(spec, "fi_area.list", request=request)
    add_pyramid_paths(spec, "fi_area.search", request=request)

    add_pyramid_paths(spec, "fi_housing_company.list_create", request=request)
    add_pyramid_paths(spec, "fi_plot_overview.list_create", request=request)

    add_pyramid_paths(spec, "public.fi_property.read", request=request)

    add_pyramid_paths(spec, "group.list_create", request=request)
    add_pyramid_paths(spec, "group.read_edit", request=request)

    add_pyramid_paths(spec, "contracts.list", request=request)
    add_pyramid_paths(spec, "webhook.external_lead", request=request)
    add_pyramid_paths(spec, "webhook.sowise", request=request)
    add_pyramid_paths(spec, "webhook.document_signed", request=request)

    add_pyramid_paths(spec, "brokerage_offer.generate", request=request)

    add_pyramid_paths(spec, "webhook.newsletter_subscribe", request=request)
    add_pyramid_paths(spec, "webhook.mailgun_event", request=request)
    add_pyramid_paths(spec, "webhook.outlook_event", request=request)

    add_pyramid_paths(spec, "company.list_create", request=request)
    add_pyramid_paths(spec, "company.read_edit", request=request)

    add_pyramid_paths(spec, "documents.get_preview", request=request)
    add_pyramid_paths(spec, "documents.get_pdf", request=request)

    add_pyramid_paths(spec, "document_signing.add_signer", request=request)
    add_pyramid_paths(spec, "document_signing.delete_signer", request=request)

    add_pyramid_paths(spec, "fi_sales_agreement.list_create", request=request)
    add_pyramid_paths(spec, "fi_sales_agreement.read_edit", request=request)
    add_pyramid_paths(spec, "fi_sales_agreement.send_for_signing", request=request)
    add_pyramid_paths(spec, "fi_sales_agreement.fetch_signing_status", request=request)
    add_pyramid_paths(spec, "fi_sales_agreement.remind_signers", request=request)

    add_pyramid_paths(spec, "fi_purchase_offer.list_create", request=request)
    add_pyramid_paths(spec, "fi_purchase_offer.read_edit", request=request)
    add_pyramid_paths(spec, "fi_purchase_offer.send_for_signing", request=request)
    add_pyramid_paths(spec, "fi_purchase_offer.fetch_signing_status", request=request)
    add_pyramid_paths(spec, "fi_purchase_offer.remind_signers", request=request)

    add_pyramid_paths(spec, "fi_counter_offer.list_create", request=request)
    add_pyramid_paths(spec, "fi_counter_offer.read_edit", request=request)
    add_pyramid_paths(spec, "fi_counter_offer.send_for_signing", request=request)
    add_pyramid_paths(spec, "fi_counter_offer.fetch_signing_status", request=request)
    add_pyramid_paths(spec, "fi_counter_offer.remind_signers", request=request)

    add_pyramid_paths(spec, "contact_v2.list_create", request=request)
    add_pyramid_paths(spec, "contact_v2.read_edit", request=request)
    add_pyramid_paths(spec, "organization.ad_template.list_create", request=request)
    add_pyramid_paths(
        spec, "organization.ad_template.read_edit_delete", request=request
    )
    add_pyramid_paths(spec, "contact_v2.list_create", request=request)
    add_pyramid_paths(spec, "contact_v2.read_edit", request=request)

    add_pyramid_paths(spec, "fi_details_of_sale.list_create", request=request)
    add_pyramid_paths(spec, "fi_details_of_sale.read_edit", request=request)
    add_pyramid_paths(spec, "fi_details_of_sale.prefill", request=request)

    add_pyramid_paths(spec, "fi_sales_data.read", request=request)

    add_pyramid_paths(spec, "document_library.items", request=request)
    add_pyramid_paths(spec, "document_library.item", request=request)
    add_pyramid_paths(spec, "document_library.start_uploads", request=request)
    add_pyramid_paths(spec, "document_library.confirm_pending_uploads", request=request)
    add_pyramid_paths(spec, "document_library.cancel_pending_uploads", request=request)
    add_pyramid_paths(spec, "document_library.send_for_signing", request=request)
    add_pyramid_paths(spec, "document_library.share_items", request=request)
    add_pyramid_paths(spec, "document_library.shared_items", request=request)
    add_pyramid_paths(spec, "document_library.download_item", request=request)
    add_pyramid_paths(spec, "document_library.download_items", request=request)
    add_pyramid_paths(spec, "document_library.document_types", request=request)

    return spec.to_dict()


def swagger_ui_script_template(request: Request, spec_route_name: str, **kwargs):
    template = pkg_resources.resource_string(
        "pyramid_apispec", "static/index_script_template.html"
    ).decode("utf8")
    swagger_spec_url = request.route_url(spec_route_name)
    if app_cfg.is_local_development():
        url = swagger_spec_url.replace("https://", "https://")
    else:
        url = swagger_spec_url.replace("http://", "https://")

    return Template(template).safe_substitute(swagger_spec_url=url)
