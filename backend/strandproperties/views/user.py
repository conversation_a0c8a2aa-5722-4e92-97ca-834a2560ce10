import uuid
from datetime import UTC, datetime, timedelta
from pathlib import Path
from string import Template
from typing import Sequence

from pydantic import ValidationError
from pyramid.exceptions import HTT<PERSON>Forbidden, HTTPNotFound
from pyramid.view import view_config
from slugify import slugify
from sqlalchemy import and_, delete, not_, or_, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import selectinload

from strandproperties.config import app_cfg
from strandproperties.constants import (
    DEVELOPER_EXCLUDE_EMAIL_DOMAINS,
    EventLogAction,
    EventLogObjectType,
    RoleType,
)
from strandproperties.libs.aws import S3Service
from strandproperties.libs.utils import (
    get_separated_values,
    random_string,
    update_model_from_schema,
)
from strandproperties.logger import logger
from strandproperties.models.auth import ResetCode
from strandproperties.models.company import Company
from strandproperties.models.office import Office, UserOffice
from strandproperties.models.tag import Tag, UserTag
from strandproperties.models.user import Role, User
from strandproperties.schemas.base import PaginatedList, StatusOk, build_page_metadata
from strandproperties.schemas.image import ImagePresignedUrlRead
from strandproperties.schemas.user import (
    ChangePreviewModePayload,
    PropertyOpenMode,
    PropertyViewMode,
    RealtorListRead,
    UserCreate,
    UserEdit,
    UserFilterParam,
    UserListRead,
    UserRead,
    UserRoleEdit,
)
from strandproperties.utils.event_log import create_event_log
from strandproperties.views.base import BaseApi
from strandproperties.views.exceptions import ApiError

STRAND_ORGANIZATION_NAME = "Strand Spain"


class UserApi(BaseApi):
    @view_config(
        route_name="user.me",
        request_method="GET",
        permission="user:read",
    )
    def me(self):
        """Current logged in user
        ---
        get:
            description: Current logged in user
            tags: ["User"]
            security:
                - BearerAuth: []
            responses:
                200:
                    content:
                        application/json:
                            schema:
                                UserRead
        """
        user = self.db_session.scalars(
            select(User)
            .options(selectinload(User._roles).selectinload(Role.organization))
            .where(User.id == self.request.identity)
        ).first()

        if user:
            return UserRead.model_validate(user)

        raise HTTPNotFound("User not found")

    @view_config(
        route_name="user.list_create",
        request_method="POST",
    )
    def create_user(self):
        """Create user endpoint
        ---
        post:
            description: Create user
            tags: ["User"]
            security:
                - BearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema: UserCreate
            responses:
                201:
                    content:
                        application/json:
                            schema: UserRead
        """

        if not self.is_admin:
            raise HTTPForbidden("Only admin users can create users.")

        try:
            params = UserCreate(**self.request.json_body)
        except ValidationError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Invalid user information")

        if params.email:
            existing_user = self.db_session.scalar(
                select(User).where(User.email == params.email)
            )
            if existing_user:
                raise ApiError(
                    status=409, message="User with this email already exists"
                )

        user_roles = [
            Role(
                organization_id=role_item.organization_id,
                role=role_item.role.value,
            )
            for role_item in params.roles
            if role_item.role is not None
        ]

        user = User(
            **params.model_dump(exclude={"roles"}),
            is_active=True,
            is_verified=True,
            _roles=user_roles,
            slug=slugify(f"{params.first_name}-{params.last_name}"),
        )

        # Create the user
        try:
            self.db_session.add(user)
            self.db_session.flush()
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Unexpected error occurred", status=500)

        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.USER,
            object_id=user.id,
            actor_id=self.user_id,
            action=EventLogAction.CREATED,
            details={
                "user": {
                    "after": {
                        "id": user.id,
                        "name": f"{user.first_name} {user.last_name}",
                    }
                }
            },
            data_after={"id": user.id},
        )

        # Send the set password email
        random_code = uuid.uuid4().hex

        reset_code = ResetCode(
            user_id=user.id,
            code=random_code,
            expired_at=datetime.now(UTC) + timedelta(hours=72),
        )

        self.db_session.add(reset_code)
        self.db_session.flush()

        self.send_set_password_email(user.email, random_code)

        self.request.response.status = 201
        return UserRead.model_validate(user)

    @view_config(
        route_name="user.list_create",
        request_method="GET",
        permission="user:read",
    )
    def list_users(self):
        """List users endpoint
        ---
        get:
            description: List users
            tags: ["User"]
            security:
                - BearerAuth: []
            parameters:
                - in: query
                  name: page
                  schema:
                    type: integer
                - in: query
                  name: pageSize
                  schema:
                    type: integer
                - in: query
                  name: tag_ids
                  schema:
                    type: string
                  description: Semicolon separated list of tag ids
                - in: query
                  name: keyword
                  schema:
                    type: string
                  description: Partial search keyword
                - in: query
                  name: organizationId
                  schema:
                    type: integer
                  description: the filter for user's organization
            responses:
                200:
                    content:
                        application/json:
                            schema: PaginatedList[UserListRead]
        """
        try:
            params = UserFilterParam(**self.request.GET)
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Invalid user filter params")

        stmt = select(User).options(
            selectinload(User.tags),
            selectinload(User._roles).selectinload(Role.organization),
            selectinload(User.offices),
        )

        if params.keyword:
            keywords = get_separated_values(params.keyword, " ")
            ors = [
                or_(
                    User.first_name.contains(k),
                    User.last_name.contains(k),
                    User.email.contains(k),
                )
                for k in keywords
            ]
            stmt = stmt.where(and_(*ors))

        if params.tag_ids:
            tag_ids = get_separated_values(params.tag_ids)
            ors = [User.tags.any(id=id) for id in tag_ids]
            stmt = stmt.where(or_(*ors))

        if params.only_active is not None:
            stmt = stmt.where(User.is_active == params.only_active)

        if params.organization_id is not None:
            stmt = stmt.where(User._roles.any(organization_id=params.organization_id))

        # Check photographer role (only queries active users)
        if self.is_photographer:
            stmt = stmt.where(User.is_active == True)

        try:
            results = (
                self.db_session.scalars(
                    stmt.fetch(params.page_size)
                    .offset((params.page - 1) * params.page_size)
                    .order_by(User.first_name, User.last_name, User.email)
                )
                .unique()
                .all()
            )
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not get list of users")

        page_metadata = build_page_metadata(
            db=self.db_session,
            query=stmt,
            page=params.page,
            page_size=params.page_size,
        )

        return PaginatedList[UserListRead](
            metadata=page_metadata,
            records=results,
        )

    @view_config(
        route_name="user.read_edit",
        request_method="GET",
        permission="user:read",
    )
    def read_user(self):
        """Get user endpoint
        ---
        get:
            description: Get user
            tags: ["User"]
            security:
                - BearerAuth: []
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                200:
                    content:
                        application/json:
                            schema: UserRead
        """
        id = self.request.matchdict["id"]

        stmt = select(User).where(User.id == id)

        try:
            item = self.db_session.scalars(stmt).unique().one()
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise HTTPNotFound()

        # Check photographer role (only queries active users)
        if self.is_photographer and not item.is_active:
            raise HTTPNotFound()

        return UserRead.model_validate(item)

    @view_config(
        route_name="user.read_edit",
        request_method="PATCH",
        permission="user:update",
    )
    def edit_user(self):
        """Edit user endpoint
        ---
        patch:
            description: Edit user
            tags: ["User"]
            security:
                - BearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema: UserEdit
            responses:
                200:
                    content:
                        application/json:
                            schema: UserRead
        """
        user_id = self.request.matchdict["id"]

        try:
            user = self.db_session.scalars(select(User).where(User.id == user_id)).one()
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise HTTPNotFound("User with this id not found")

        if not self.is_admin and self.user_id != user.id:
            raise HTTPForbidden()

        try:
            params = UserEdit(**self.request.json_body)
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Invalid user information")

        if params.photo_url:
            # S3 url => Cloudfront url
            params.photo_url = params.photo_url.replace(
                app_cfg.aws_s3_bucket_url,
                app_cfg.aws_cloudfront_url,
            )

        # params.dias_api_key: None will be ignored -> not set, keep existing value
        # params.dias_api_key: " " will be set value to None -> remove dias api key
        if hasattr(params, "dias_api_key") and isinstance(params.dias_api_key, str):
            if not self.is_admin or not self.is_fi_org:
                raise HTTPForbidden("Only FI Admin can set user dias api key")
            user.set_dias_api_key(params.dias_api_key)
            del params.dias_api_key

        try:
            changed_fields = update_model_from_schema(
                model_obj=user,
                schema_obj=params,
                exclude={
                    "new_tags",
                    "existing_tags",
                    "new_offices",
                    "existing_offices",
                },
            )
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not update user")

        ## Handle office tags
        extra_params = params.model_dump()

        new_tags = extra_params.get("new_tags", []) or []
        existing_tags = extra_params.get("existing_tags", []) or []

        new_offices = extra_params.get("new_offices", []) or []
        existing_offices = extra_params.get("existing_offices", []) or []

        try:
            self.db_session.add_all(
                Tag(
                    name=tag,
                )
                for tag in new_tags
            )
            self.db_session.flush()
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not create office tag(s)")

        self.db_session.add_all(Office(name=office) for office in new_offices)

        try:
            new_tags_ids = (
                self.db_session.execute(select(Tag.id).where(Tag.name.in_(new_tags)))
                .scalars()
                .all()
            )
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not get list of office tags")

        new_offices_ids = (
            self.db_session.execute(
                select(Office.id).where(Office.name.in_(new_offices))
            )
            .scalars()
            .all()
        )

        merged_tag_ids = list(set(new_tags_ids + existing_tags))
        merged_office_ids = list(set(new_offices_ids + existing_offices))

        try:
            self.handle_user_many_to_many_relationship(
                user.id, merged_tag_ids, UserTag, "tag_id"
            )
            self.db_session.flush()
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not create user relationship with tag(s)")

        self.handle_user_many_to_many_relationship(
            user.id, merged_office_ids, UserOffice, "office_id"
        )

        # Handle company relationship
        if params.company_id is not None:
            company = self.db_session.scalars(
                select(Company).where(Company.id == params.company_id)
            ).one_or_none()
            if not company:
                raise ApiError(status=404, message="Company not found.")
            user.company = company
        self.db_session.flush()

        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.USER,
            object_id=user.id,
            actor_id=self.user_id,
            action=EventLogAction.UPDATED,
            details={
                "user": {
                    "before": {k: v["before"] for k, v in changed_fields.items()},
                    "after": {k: v["after"] for k, v in changed_fields.items()},
                }
            },
            data_before={k: v["before"] for k, v in changed_fields.items()},
            data_after={k: v["after"] for k, v in changed_fields.items()},
        )

        self.request.response.status = 200
        return UserRead.model_validate(user)

    @view_config(
        route_name="user.activate",
        request_method="POST",
    )
    def activate_user(self):
        """Activate user endpoint
        ---
        patch:
            description: Activate user
            tags: ["User"]
            security:
                - BearerAuth: []
            responses:
                200:
                    description: User activated successfully.
        """
        user_id = self.request.matchdict["id"]

        try:
            user = self.db_session.scalars(select(User).where(User.id == user_id)).one()
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise HTTPNotFound("User with this id not found")

        if not self.is_admin:
            raise HTTPForbidden()

        if user.is_active:
            raise ApiError("User is already active")

        if user.id == self.request.identity:
            raise ApiError("User cannot activate itself")

        user.is_active = True

        self.request.response.status = 200
        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.USER,
            object_id=user.id,
            actor_id=self.user_id,
            action=EventLogAction.ACTIVATED,
            data_before={"is_active": False},
            data_after={"is_active": True},
        )
        return {"status": "ok"}

    @view_config(
        route_name="user.deactivate",
        request_method="POST",
    )
    def deactivate_user(self):
        """Deactivate user endpoint
        ---
        patch:
            description: Deactivate user
            tags: ["User"]
            security:
                - BearerAuth: []
            responses:
                200:
                    description: User deactivated successfully.
        """
        user_id = self.request.matchdict["id"]

        try:
            user = self.db_session.scalars(select(User).where(User.id == user_id)).one()
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise HTTPNotFound("User with this id not found")

        if not self.is_admin:
            raise HTTPForbidden()

        if not user.is_active:
            raise ApiError("User is already inactive")

        if user.id == self.request.identity:
            raise ApiError("User cannot deactivate itself")

        user.is_active = False

        self.request.response.status = 200

        create_event_log(
            db_session=self.db_session,
            object_type=EventLogObjectType.USER,
            object_id=user.id,
            actor_id=self.user_id,
            action=EventLogAction.DEACTIVATED,
            data_before={"is_active": True},
            data_after={"is_active": False},
        )
        return {"status": "ok"}

    @view_config(
        route_name="user.change_preview_mode", request_method="PATCH", renderer="json"
    )
    def change_preview_mode(self):
        user_id = self.request.matchdict["id"]

        try:
            payload = ChangePreviewModePayload(**self.request.json_body)
        except ValidationError:
            raise ApiError("Invalid payload")

        try:
            user = self.db_session.scalars(select(User).where(User.id == user_id)).one()
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise HTTPNotFound("User with this id not found")

        user.details = user.details or {}

        # Always store string values
        if payload.property_view_mode is not None:
            user.details["property_view_mode"] = payload.property_view_mode.value
        if payload.property_open_mode is not None:
            user.details["property_open_mode"] = payload.property_open_mode.value

        self.db_session.flush()

        return {
            "status": "ok",
            "property_view_mode": user.details.get("property_view_mode"),
            "property_open_mode": user.details.get("property_open_mode"),
        }

    @view_config(
        route_name="user_image.generate_presigned_urls",
        request_method="POST",
        permission="user_image:create",
    )
    def generate_presigned_urls(self):
        """Generate presigned user profile picture urls endpoint
        ---
        post:
            description: Generate presigned user profile picture urls endpoint
            tags: ["Image"]
            security:
                - BearerAuth: []
            requestBody:
                required: true
                description: An array of filenames
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                type: string
            responses:
                201:
                    description: Generated URLs, contains original filenames and filenames on uploaded
                    content:
                        application/json:
                            schema:
                                type: array
                                items: ImagePresignedUrlRead
        """
        results: list[ImagePresignedUrlRead] = []
        user_name_or_email = self.request.matchdict["user_name_or_email"]

        try:
            s3_service = S3Service(app_cfg, app_cfg.aws_s3_bucket_name)
            for filename in self.request.json_body:
                filepath = Path(filename)
                suffix = filepath.suffix.lower()
                if suffix not in [
                    ".jpg",
                    ".jpeg",
                    ".png",
                    ".webp",
                ]:
                    raise ApiError("Invalid image")

                upload_path = f"userprofile/{user_name_or_email}-{random_string(length=16)}{suffix}"
                url = s3_service.generate_upload_url(upload_path)

                results.append(
                    ImagePresignedUrlRead(url=url, original_filename=filename)
                )
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not generate image presigned url")

        self.request.response.status = 201
        return results

    @view_config(
        route_name="user.change_role",
        request_method="PATCH",
        permission="user:change_role",
        openapi_param_schema=UserRoleEdit,
        openapi_response_schema=StatusOk,
        openapi_metadata={
            "tags": ["User"],
        },
    )
    def change_role(self):
        user_id = self.request.matchdict["id"]

        try:
            user = self.db_session.scalars(select(User).where(User.id == user_id)).one()
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise HTTPNotFound("User with this id not found")

        if not self.is_admin:
            raise HTTPForbidden()

        if self.user_id == user.id:
            raise ApiError("User cannot change his own role")

        try:
            params = UserRoleEdit(**self.request.json_body)
        except Exception as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Invalid user role information")

        try:
            # Iterate over the roles provided in the request
            for role_item in params.roles:
                # Delete any existing role for this organization and user
                self.db_session.execute(
                    delete(Role).where(
                        and_(
                            Role.organization_id == role_item.organization_id,
                            Role.user_id == user_id,
                        )
                    )
                )

                # If a role is provided, add it
                if role_item.role is not None:
                    new_role = Role(
                        organization_id=role_item.organization_id,
                        user_id=user_id,
                        role=role_item.role,
                    )
                    self.db_session.add(new_role)

            self.db_session.flush()
        except SQLAlchemyError as e:
            logger.error(e, extra={"requestId": self.request.request_id})
            raise ApiError("Could not update user roles")

        self.request.response.status = 200
        return {"status": "ok"}

    def handle_user_many_to_many_relationship(self, user_id, selected_ids, table, key):
        current_ids = self.db_session.scalars(
            select(getattr(table, key)).where(table.user_id == user_id)
        ).all()

        for selected_id in current_ids:
            if selected_id not in selected_ids:
                self.db_session.execute(
                    delete(table).where(
                        table.user_id == user_id,
                        getattr(table, key) == selected_id,
                    )
                )

        for selected_id in selected_ids:
            if selected_id not in current_ids:
                model_item = table()
                setattr(model_item, key, selected_id)
                model_item.user_id = user_id
                self.db_session.add(model_item)

    def send_set_password_email(self, email: str | Sequence[str], code: str):
        """Send the reset password link to the user by email."""
        # This functionality is remove because of the SSO flow
        # change_password_link = (
        #     f"{app_cfg.strand_client_base_url}/changepassword?code={code}"
        # )
        # msgBody = mail_template.substitute(
        #     {"reset_password_link": change_password_link}
        # )
        # subject = "Password Reset Instructions for MyStrand"

        # try:
        #     send_msg_by_email(email, msgBody, subject)

        # except Exception as e:
        #     logger.error(e, extra={"requestId": self.request.request_id})
        #     raise ApiError("Could not send reset code email.")


class RealtorAPI(BaseApi):
    default_validators = ["validate_api_key"]

    @view_config(
        route_name="user.list_realtors",
        permission="__no_permission_required__",
        request_method="GET",
        openapi_metadata={
            "tags": ["User"],
        },
    )
    def list_realtors(self):
        realtors = self.db_session.scalars(
            select(User)
            .join(User._roles)
            .where(
                User._roles.any(Role.role == RoleType.REALTOR),
                not_(
                    or_(
                        User.email.contains(mail_domain)
                        for mail_domain in DEVELOPER_EXCLUDE_EMAIL_DOMAINS
                    )
                ),
            )
        ).all()
        self.request.response.status = 200
        return [RealtorListRead.model_validate(realtor) for realtor in realtors]


mail_template = Template(
    """You're receiving this email because an admin has created a MyStrand account for you. To proceed, please click the link below to set your password and login:
$reset_password_link
"""
)
