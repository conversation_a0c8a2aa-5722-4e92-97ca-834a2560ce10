import importlib
import json
import os
import pkgutil
import random
import string
from datetime import datetime, time
from email.mime.text import MIMEText
from smtplib import SMTP
from typing import Optional, Sequence
from urllib.parse import urlparse

import pytz
import requests
from cryptography.fernet import Fernet

from strandproperties.config import app_cfg
from strandproperties.constants import DEFAULT_CURRENCY, CommissionTypeEnum
from strandproperties.logger import logger

ALLOW_VIDEO_DOMAINS = [
    "youtu.be",
    "youtube.com",
    "www.youtube.com",
    "vimeo.com",
]


class EncryptionError(Exception):
    pass


def allowed_streaming_video(url: str) -> bool:
    link = urlparse(url)
    return link.netloc in ALLOW_VIDEO_DOMAINS


def import_all(package_path: str) -> int:
    """
    Import all modules from a dotted package path, e.g: `mypackage.services`
    """
    ospath = package_path.replace(".", os.sep)
    imported = 0
    for _, name, _ in pkgutil.iter_modules([ospath]):
        importlib.import_module(f"{package_path}.{name}")
        imported += 1

    return imported


def get_separated_values(values: Optional[str], separator=";") -> list[str]:
    """
    Get list of values from separated list: 1;2;3;4
    """
    if not values:
        return []
    return [id_ for id_ in values.split(separator) if id_]


def update_model_from_schema(model_obj, schema_obj, exclude=None):
    data = schema_obj.dict(exclude_unset=True, exclude=exclude)
    changed_fields = {}
    for field in data:
        if hasattr(model_obj, field):
            old_value = getattr(model_obj, field)
            new_value = data[field]

            if old_value != new_value:
                changed_fields[field] = {
                    "before": old_value,
                    "after": new_value,
                }
        setattr(model_obj, field, data[field])
    return changed_fields


CHAR_LIST = string.ascii_letters + string.digits


def random_string(length=8):
    result = ""
    for i in range(length):
        result += random.choice(CHAR_LIST)
    return result


def get_strandproperties_property_slug(
    *, reference: str, portals: dict[str, bool]
) -> Optional[str]:
    is_published_to_strandproperties = portals.get("is_strandproperties_enabled", False)

    if not is_published_to_strandproperties:
        return None

    return f"https://strandproperties.com/single-property/?ref={reference}"


def remove_some_ASCII_control_characters(input: str) -> str:
    return input.replace("", " ").replace(chr(0), "")


def send_msg_by_email(email: str | Sequence[str], msgBody: str, subject: str):
    """Send the reset code to the user by email."""

    msg = MIMEText(msgBody, "plain")
    msg["Subject"] = subject

    with SMTP(host=app_cfg.smtp_host, port=app_cfg.smtp_port) as smtp:
        smtp.starttls()
        smtp.login(app_cfg.smtp_user, app_cfg.smtp_password)
        smtp.sendmail(
            app_cfg.smtp_user,
            email,
            msg.as_string(),
        )


def get_formatted_commission_based_on_commission_type(
    *,
    commission: Optional[float],
    commission_type: Optional[CommissionTypeEnum],
    currency: Optional[str],
) -> str:
    if commission is None:
        return ""

    if commission_type is not None and commission_type == CommissionTypeEnum.FIXED:
        return f"{commission} {currency.upper() if currency else DEFAULT_CURRENCY}"

    return f"{commission}%"


def get_fernet() -> Fernet:
    key = app_cfg.fernet_key_string
    try:
        return Fernet(key)
    except Exception:
        raise EncryptionError(f"Fernet key not valid: {key}")


def encrypt(message: str) -> str | None:
    fernet = get_fernet()

    if not message or not isinstance(message, str) or message.strip() == "":
        return None
    try:
        message_bytes = message.encode("utf-8")
        encrypted_message_bytes = fernet.encrypt(message_bytes)
        encrypted_message_string = encrypted_message_bytes.decode("utf-8")
    except Exception as e:
        raise EncryptionError(f"Unexpected error during encryption: {e}") from e
    return encrypted_message_string


def decrypt(encrypted_message: str) -> str | None:
    fernet = get_fernet()

    if not encrypted_message:
        return None
    try:
        decrypted_message_bytes = fernet.decrypt(encrypted_message.encode("utf-8"))
        decrypted_message_string = decrypted_message_bytes.decode("utf-8")
    except Exception as e:
        raise EncryptionError(f"Unexpected error during decryption: {e}") from e
    return decrypted_message_string


def snake2camel(snake_str):
    first, *others = snake_str.split("_")
    return "".join([first.lower(), *map(str.title, others)])


def camel2snake(camel_str):
    return "".join(["_" + c.lower() if c.isupper() else c for c in camel_str]).lstrip(
        "_"
    )


def preprocess_params(query_params: dict, list_type_params: list[str]) -> dict:
    """
    Process query parameters from request, and force some parameters to be list type.
    Also deal with quirk like removing [] from param's name so it's easier to deal with.

    :param query_params: original query parameters from request: `request.GET.mixed()`
    :param list_type_params: List of parameter names that should be forced to be list
    """
    new_query_params = {}

    for k, v in query_params.items():
        if (
            k.endswith("[]")
            or k in list_type_params
            or camel2snake(k) in list_type_params
        ):
            # Drop the [] from the param name
            key = k.removesuffix("[]")

            # Force to list even when there is a single value
            value = v
            if v is not None and not isinstance(v, list):
                value = [v]

            new_query_params[key] = value
        else:
            new_query_params[k] = v

    return new_query_params


def is_dict_format(string: str):
    try:
        parsed = json.loads(string)
        return isinstance(parsed, dict)
    except json.JSONDecodeError:
        return False


def to_utc_end_of_finnish_day(date_str: str) -> datetime:
    """
    Convert a YYYY-MM-DD date string to an ISO 8601 UTC datetime string
    representing 23:59:59 in Finnish time (Europe/Helsinki).

    Args:
        date_str (str): Date in 'YYYY-MM-DD' format.

    Returns:
        datetime: A timezone-aware UTC datetime object
    """
    helsinki = pytz.timezone("Europe/Helsinki")

    date = datetime.strptime(date_str, "%Y-%m-%d")
    local_dt = helsinki.localize(datetime.combine(date, time(23, 59, 59)))
    utc_dt = local_dt.astimezone(pytz.UTC)

    return utc_dt


def purge_website_cache(reference: str) -> None:
    """
    Purge strandproperties.com cache for a given reference
    """
    try:
        response = requests.get(
            f"https://strandproperties.com/single-property/?ref={reference}&purge_from_cache"
        )
        response.raise_for_status()
    except Exception as e:
        logger.error("Failed to purge website cache for reference %s: %s", reference, e)
