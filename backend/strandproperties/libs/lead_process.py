import json
from datetime import datetime
from typing import List, Optional

from sqlalchemy.orm import Session

from strandproperties.constants import (
    CountryC<PERSON>,
    StatusLeadProcess,
    StatusProcessLeadData,
)
from strandproperties.logger import logger
from strandproperties.models.contact import Contact, ContactPhone, ContactRealtor
from strandproperties.models.lead import Lead, LeadContact, LeadUser
from strandproperties.models.organization import Organization
from strandproperties.models.property import Property, PropertyRealtor
from strandproperties.models.raw_lead_data import RawLeadData
from strandproperties.models.user import User
from strandproperties.schemas.lead import LeadCreateEdit
from strandproperties.utils.email import email_services

SINGLE_ASSIGNED_REALTOR = 1


class LeadProcess:
    def __init__(
        self,
        db_session: Session,
        id: str,
        email: Optional[str],
        name: Optional[str],
        phone: Optional[str],
        content: Optional[str],
        source_name: str,
        reference_code: Optional[str] = None,
        campaign_name: Optional[str] = None,
        organization_id: Optional[int] = None,
        realtor_email: Optional[str] = None,
        organization: Optional[str] = None,
    ):
        self.source_lead_id = id
        self.retailer_item_id = reference_code
        self.email = email
        self.name = name or ""
        self.phone = phone or ""
        self.campaign_name = campaign_name
        self.content = content
        self.db_session = db_session
        self.source_name = source_name
        self.campaign_name = campaign_name
        self.organization_id = organization_id
        self.realtor_email = realtor_email
        self.organization = organization

    def get_property_by_reference(self, retailer_item_id: str) -> Optional[Property]:
        return (
            self.db_session.query(Property)
            .filter(Property.reference == retailer_item_id)
            .one_or_none()
        )

    def get_contact_by_email(self, email: Optional[str]) -> Optional[Contact]:
        if email is None:
            return None
        return self.db_session.query(Contact).filter(Contact.email == email).first()

    def create_contact(
        self,
        email: Optional[str],
        name: str,
        phone: str,
        organization_id: int,
        source: str = None,
    ) -> Contact:
        contact = Contact(
            email=email,
            name=name,
            phones=[ContactPhone(phone_number=phone)],
            organization_id=organization_id,
            created_at=datetime.now(),
            source=source,
        )
        self.db_session.add(contact)
        self.db_session.flush()
        return contact

    def update_organization_for_contact(
        self, contact: Contact, organization_id: int
    ) -> Contact:
        contact.organization_id = organization_id
        self.db_session.add(contact)
        self.db_session.flush()

    def assign_contact_to_realtors(
        self,
        contact_id: int,
        property_item: Optional[Property],
        realtor_email: Optional[str],
    ) -> List[int]:
        if property_item is None:
            realtor = (
                self.db_session.query(User)
                .filter(User.email == realtor_email)
                .one_or_none()
            )
            if not realtor:
                return []

            realtor_ids = [realtor.id]
        else:
            # Get all realtor IDs associated with the property in one query
            realtor_ids = [
                user_id
                for (user_id,) in self.db_session.query(PropertyRealtor.user_id)
                .filter(PropertyRealtor.property_id == property_item.id)
                .all()
            ]
            if not realtor_ids:
                return []

        # Create all ContactRealtor objects at once
        contact_realtors = [
            ContactRealtor(contact_id=contact_id, realtor_id=realtor_id)
            for realtor_id in realtor_ids
        ]

        self.db_session.bulk_save_objects(contact_realtors)
        self.db_session.flush()

        logger.info(f"Assigned contact {contact_id} to realtors {realtor_ids}")
        return realtor_ids

    def save_raw_lead_data(
        self,
        source_lead_id: str,
        phone: Optional[str],
        email: Optional[str],
        campaign_name: Optional[str],
        content: str,
        retailer_item_id: Optional[str] = None,
        full_name: Optional[str] = None,
        realtor_email: Optional[str] = None,
        organization: Optional[str] = None,
    ) -> RawLeadData:
        try:
            parsed_content = json.loads(content) if content else None
        except json.JSONDecodeError:
            parsed_content = content

        raw_lead_data = RawLeadData(
            source_lead_id=source_lead_id,
            full_name=full_name,
            phone=phone,
            email=email,
            campaign_name=campaign_name,
            status=StatusProcessLeadData.UNPROCESSED,
            contact_id=None,
            property_reference=retailer_item_id,
            content=parsed_content,
            source=self.source_name,
            is_manual_assign=False,
            realtor_email=realtor_email,
            organization=organization,
        )
        self.db_session.add(raw_lead_data)
        self.db_session.flush()
        logger.info(
            f"Saved raw lead data: {raw_lead_data.id} {raw_lead_data.full_name}"
        )

        return raw_lead_data

    def user_exists(self, user_id: int) -> Optional[User]:
        return self.db_session.query(User).filter(User.id == user_id).one_or_none()

    def create_sales_activity_to_assigned_realtor(
        self,
        assigned_to_users: list,
        property_reference: Optional[str],
        contact_ids: list,
        title: str,
        description: str,
        lead_name: str,
        organization_id: int,
        source_name: str,
    ) -> Lead:
        sale_activity_params = LeadCreateEdit(
            assigned_to=assigned_to_users,
            property_reference=property_reference,
            contact_ids=contact_ids,
            title=title,
            description=description,
            source=source_name,
        )

        sale_activity = Lead(
            **sale_activity_params.model_dump(exclude={"assigned_to", "contact_ids"}),
            organization_id=organization_id,
        )
        self.db_session.add(sale_activity)
        self.db_session.flush()
        logger.info(
            f"Created sales activity: {sale_activity.title} {sale_activity.property_reference}"
        )

        for user_id in assigned_to_users:
            realtor = self.user_exists(user_id)
            if realtor and realtor.is_active:
                lead_assigned_to = LeadUser(
                    lead_id=sale_activity.id,
                    user_id=user_id,
                )
                self.db_session.add(lead_assigned_to)
                email_services.notify_realtor_for_sale_activity(
                    receivers=[realtor.email],
                    first_name=realtor.first_name,
                    last_name=realtor.last_name,
                    sale_activity_id=sale_activity.id,
                    lead_name=lead_name,
                )
                logger.info(
                    f"Sent lead email to {realtor.email} for sale activity {sale_activity.id}"
                )
        self.db_session.flush()

        self.db_session.bulk_save_objects(
            [
                LeadContact(lead_id=sale_activity.id, contact_id=contact_id)
                for contact_id in contact_ids
            ]
        )
        logger.info(
            f"Assigned sale activity {sale_activity.id} to contact {contact_ids}"
        )
        self.db_session.flush()

        return sale_activity

    def check_ignore_lead(self, source_lead_id: str, source_name: str) -> bool:
        return self.db_session.query(
            self.db_session.query(RawLeadData)
            .filter(
                RawLeadData.source_lead_id == source_lead_id,
                RawLeadData.source == source_name,
            )
            .exists()
        ).scalar()

    def is_email_and_phone_empty(
        self, email: Optional[str], phone: Optional[str]
    ) -> bool:
        return not email and not phone

    def update_raw_lead_data_and_status(
        self,
        contact_id: int,
        is_manual_assign: bool,
        status: StatusProcessLeadData,
        raw_lead_data: RawLeadData,
        is_new_contact: bool,
    ):
        raw_lead_data.contact_id = contact_id
        raw_lead_data.is_manual_assign = is_manual_assign
        raw_lead_data.status = status
        raw_lead_data.is_new_contact = is_new_contact
        self.db_session.add(raw_lead_data)
        self.db_session.flush()
        logger.info(
            f"Updated raw lead data with contact_id: {contact_id}, status: {status.name}"
        )

    def is_property_realtor_active(self, property_item: Property) -> bool:
        property_realtors = (
            self.db_session.query(PropertyRealtor)
            .filter(PropertyRealtor.property_id == property_item.id)
            .all()
        )

        active_realtors = [
            realtor
            for realtor in property_realtors
            if (user := self.user_exists(realtor.user_id)) and user.is_active
        ]

        return bool(active_realtors)

    def get_realtor_active(self, realtor_email: str):
        if not realtor_email:
            return None
        return (
            self.db_session.query(User)
            .filter(User.email == realtor_email, User.is_active == True)
            .one_or_none()
        )

    def process_existing_contact(
        self,
        contact: Contact,
        property_item: Optional[Property],
        raw_lead_data: RawLeadData,
    ):
        contact_id = contact.id
        logger.info(f"Contact {contact_id} already exists.")
        assigned_users = contact.assigned_to_users
        assigned_to_users = [user.id for user in assigned_users if user.is_active]

        active_preselected_realtor = self.get_realtor_active(self.realtor_email)
        if active_preselected_realtor:
            if active_preselected_realtor.id not in assigned_to_users:
                contact_assigned_to_realtor = [
                    ContactRealtor(
                        contact_id=contact_id, realtor_id=active_preselected_realtor.id
                    )
                ]
                self.db_session.bulk_save_objects(contact_assigned_to_realtor)
                self.db_session.flush()
            assigned_to_users = [active_preselected_realtor.id]
        else:
            if not assigned_to_users:
                if not property_item or not self.is_property_realtor_active(
                    property_item=property_item
                ):
                    self.update_raw_lead_data_and_status(
                        contact_id=contact_id,
                        is_manual_assign=True,
                        status=StatusProcessLeadData.UNPROCESSED,
                        raw_lead_data=raw_lead_data,
                        is_new_contact=False,
                    )
                    return StatusLeadProcess.SUCCESS

                self.update_organization_for_contact(
                    contact=contact, organization_id=property_item.organization_id
                )

                assigned_to_users = self.assign_contact_to_realtors(
                    contact_id=contact_id,
                    property_item=property_item,
                    realtor_email=self.realtor_email,
                )
            else:
                if len(assigned_to_users) > SINGLE_ASSIGNED_REALTOR:
                    self.update_raw_lead_data_and_status(
                        contact_id=contact_id,
                        is_manual_assign=True,
                        status=StatusProcessLeadData.UNPROCESSED,
                        raw_lead_data=raw_lead_data,
                        is_new_contact=False,
                    )
                    return StatusLeadProcess.SUCCESS

        self.create_sales_activity_to_assigned_realtor(
            assigned_to_users=assigned_to_users,
            property_reference=(
                property_item.reference if property_item is not None else None
            ),
            contact_ids=[contact_id],
            title=f"[{self.source_name}] {contact.name}",
            description=json.dumps(self.content, indent=2),
            lead_name=raw_lead_data.full_name,
            organization_id=contact.organization_id,
            source_name=f"{raw_lead_data.source} / {raw_lead_data.campaign_name}",
        )
        self.update_raw_lead_data_and_status(
            contact_id=contact_id,
            is_manual_assign=False,
            status=StatusProcessLeadData.PROCESSED,
            raw_lead_data=raw_lead_data,
            is_new_contact=False,
        )

    def process_new_contact(
        self, raw_lead_data: RawLeadData, property_item: Optional[Property]
    ):
        contact = self.create_contact(
            self.email,
            self.name,
            self.phone,
            self.organization_id,
            f"{raw_lead_data.source} / {raw_lead_data.campaign_name}",
        )
        contact_id = contact.id

        is_manual_assign = False
        status = StatusProcessLeadData.PROCESSED

        active_preselected_realtor = self.get_realtor_active(self.realtor_email)
        if active_preselected_realtor:
            assigned_to_users = self.assign_contact_to_realtors(
                contact_id=contact_id,
                property_item=property_item,
                realtor_email=self.realtor_email,
            )
        else:
            if property_item and self.is_property_realtor_active(property_item):
                self.update_organization_for_contact(
                    contact=contact, organization_id=property_item.organization_id
                )
                assigned_to_users = self.assign_contact_to_realtors(
                    contact_id=contact_id,
                    property_item=property_item,
                    realtor_email=self.realtor_email,
                )
            else:
                self.assign_contact_to_realtors(
                    contact_id=contact_id,
                    property_item=property_item,
                    realtor_email=self.realtor_email,
                )

                is_manual_assign = True
                status = StatusProcessLeadData.UNPROCESSED
                self.update_raw_lead_data_and_status(
                    contact_id=contact_id,
                    is_manual_assign=is_manual_assign,
                    status=status,
                    raw_lead_data=raw_lead_data,
                    is_new_contact=True,
                )
                return StatusLeadProcess.SUCCESS

        self.create_sales_activity_to_assigned_realtor(
            assigned_to_users=assigned_to_users,
            property_reference=property_item.reference if property_item else None,
            contact_ids=[contact_id],
            title=f"[{self.source_name}] {contact.name}",
            description=json.dumps(self.content, indent=2),
            lead_name=raw_lead_data.full_name,
            organization_id=contact.organization_id,
            source_name=f"{raw_lead_data.source} / {raw_lead_data.campaign_name}",
        )

        self.update_raw_lead_data_and_status(
            contact_id=contact_id,
            is_manual_assign=is_manual_assign,
            status=status,
            raw_lead_data=raw_lead_data,
            is_new_contact=True,
        )

    def process(self, property_item: Optional[Property]):
        raw_lead_data = self.save_raw_lead_data(
            source_lead_id=self.source_lead_id,
            phone=self.phone,
            email=self.email,
            campaign_name=self.campaign_name,
            content=self.content,
            retailer_item_id=self.retailer_item_id,
            full_name=self.name,
            realtor_email=self.realtor_email,
            organization=self.organization,
        )

        if self.is_email_and_phone_empty(email=self.email, phone=self.phone):
            logger.info(
                f"Source lead ID {self.source_lead_id} has empty email and phone."
            )
            return StatusLeadProcess.ERROR_MISSING_EMAIL_OR_PHONE

        contact = self.get_contact_by_email(self.email)

        if contact:
            self.process_existing_contact(contact, property_item, raw_lead_data)
        else:
            self.process_new_contact(raw_lead_data, property_item)

        logger.info(
            f"Lead processing completed for source_lead_id: {self.source_lead_id}"
        )
        return StatusLeadProcess.SUCCESS

    def execute(self, mode: str):
        property_item = self.get_property_by_reference(self.retailer_item_id)

        if property_item:
            self.organization_id = property_item.organization_id
        else:
            if not self.organization:
                self.organization = "es"
                if self.phone:
                    self.organization = "fi" if self.phone.startswith("+358") else "es"

            country_code = (
                CountryCode.SPAIN if self.organization == "es" else CountryCode.FINLAND
            )

            self.organization_id = (
                self.db_session.query(Organization.id)
                .filter(
                    Organization.country_code == country_code,
                    Organization.language == self.organization,
                )
                .scalar()
            )

        if self.check_ignore_lead(
            source_lead_id=self.source_lead_id, source_name=self.source_name
        ):
            logger.info(
                f"Source lead ID {self.source_lead_id} already processed or ignored."
            )
            return StatusLeadProcess.ERROR_IGNORE_LEAD

        logger.info(
            f"[{self.source_name}] Processing source lead ID {self.source_lead_id}."
        )

        if mode == "process":
            self.db_session.commit()

            with self.db_session.begin():
                return self.process(property_item=property_item)
        else:
            return self.process(property_item=property_item)
