from pyramid.config import Configurator


def includeme(config: Configurator):
    # openapi
    config.include("pyramid_apispec.views")
    config.add_route("openapi_spec", "/-/openapi.json")
    config.pyramid_apispec_add_explorer(
        explorer_route_path="/-/spec",
        spec_route_name="openapi_spec",
        ui_version="4.18.3",
        script_generator="strandproperties.views.spec:swagger_ui_script_template",
        permission="__no_permission_required__",
    )

    # healthcheck and version
    config.add_route("healthy", "/-/healthy")

    # sandbox
    config.add_route("sandbox.unexpected_exception", "/-/sandbox/unexpected-exception")
    config.add_route("sandbox.validation_error", "/-/sandbox/validation-error")
    config.add_route("sandbox.huey_tasks", "/-/sandbox/huey-tasks")

    # auth
    config.add_route("auth.login", "/auth/login")
    config.add_route("auth.get_user_with_sso", "/auth/get-user-with-sso")
    config.add_route("auth.reset_password.generate", "/auth/reset-password/generate")
    config.add_route("auth.reset_password", "/auth/reset-password")

    # webhook
    config.add_route("webhook.sowise", "/webhook/sowise")
    config.add_route("webhook.external_lead", "/webhook/external-lead")
    config.add_route("webhook.document_signed", "/webhook/document-signed")
    config.add_route("inmobalia", "/inmobalia")
    config.add_route("webhook.dias", "/webhook/dias")
    config.add_route("webhook.newsletter_subscribe", "/webhook/newsletter-subscribe")
    config.add_route("webhook.mailgun_event", "/webhook/mailgun-event")
    config.add_route("webhook.outlook_event", "/webhook/outlook-event")

    # user
    config.add_route("user.me", "/me")
    config.add_route("user.list_create", "/users")
    config.add_route("user.read_edit", "/users/{id}")
    config.add_route("user.activate", "/users/{id}/activate")
    config.add_route("user.deactivate", "/users/{id}/deactivate")
    config.add_route("user.change_role", "/users/{id}/change-role")
    config.add_route("user.change_preview_mode", "/users/{id}/change-preview-mode")
    config.add_route(
        "user_image.generate_presigned_urls",
        "user/{user_name_or_email}/image/presign",
    )
    config.add_route("user.list_realtors", "/user/realtors")

    # contact
    config.add_route("contact.list_create", "/contact")
    config.add_route("contact.list_preview", "/contacts-preview")
    config.add_route("contact.read_edit", "/contact/{id}")
    config.add_route(
        "contact.assign_multiple_contacts", "/contact-assign-multiple-agents"
    )

    config.add_route("contact_v2.list_create", "/v2/contact")
    config.add_route("contact_v2.read_edit", "/v2/contact/{id}")

    # Contact marketing consent
    config.add_route(
        "contact.marketing_consents", "/contact/{contact_id}/marketing-consents"
    )
    config.add_route(
        "contact.marketing_consent",
        "/contact/{contact_id}/marketing-consents/{newsletter_type}",
    )

    config.add_route("public.consent_settings", "/consent-settings/{token}")
    config.add_route("public.unsubscribe_user", "/unsubscribe-user/{token}")

    # nationality
    config.add_route("nationality.list", "/nationalities")

    # document_library
    config.add_route(
        "document_library.items", "/document-library-items/{owner_type}/{owner_id:\\d+}"
    )
    config.add_route(
        "document_library.item",
        "/document-library-items/{owner_type}/{owner_id:\\d+}/{item_id:\\d+}",
    )
    config.add_route(
        "document_library.start_uploads",
        "/document-library-items/{owner_type}/{owner_id:\\d+}/start-uploads",
    )
    config.add_route(
        "document_library.confirm_pending_uploads",
        "/document-library-items/{owner_type}/{owner_id:\\d+}/confirm-uploads",
    )
    config.add_route(
        "document_library.cancel_pending_uploads",
        "/document-library-items/{owner_type}/{owner_id:\\d+}/cancel-uploads",
    )
    config.add_route(
        "document_library.send_for_signing",
        "/document-library-items/{owner_type}/{owner_id:\\d+}/send-for-signing",
    )
    config.add_route(
        "document_library.share_items",
        "/document-library-items/{owner_type}/{owner_id:\\d+}/share-items",
    )
    config.add_route(
        "document_library.document_types",
        "/document-library-items/{owner_type}/{owner_id:\\d+}/document-types",
    )
    config.add_route(
        "document_library.shared_items",
        "/document-library-items/shared/{token}",
    )
    config.add_route(
        "document_library.download_item",
        "/document-library-items/download/{token}",
    )
    config.add_route(
        "document_library.download_items",
        "/document-library-items/{owner_type}/{owner_id:\\d+}/download-multiple",
    )

    # Event logs
    config.add_route("event_logs.activity", "/event-logs/{object_type}/{id}/activity")
    config.add_route("event_logs.custom", "/event-logs")

    # property
    config.add_route("property.list_create", "/properties")
    single_property_route = "/properties/{reference_code}"
    config.add_route("property.read_edit", single_property_route)
    config.add_route(
        "property.get_keyword_suggestions", "/properties/autocomplete/keyword/{query}"
    )
    config.add_route("property.change_status", f"{single_property_route}/change-status")
    config.add_route("property.upload_document", "/properties/{id}/documents")
    config.add_route("property.validation", "/properties/{id}/validation")
    config.add_route("property.duplicate", "/properties/{id}/duplicate")

    # finnish property
    config.add_route("fi_property.list_create", "/fi-properties")
    config.add_route(
        "fi_property.import_from_kivi",
        "/fi-properties/import-from-kivi",
    )
    config.add_route("fi_property.read_edit", "/fi-properties/{reference_code}")
    config.add_route("fi_property.duplicate", "/fi-properties/{id}/duplicate")
    config.add_route(
        "fi_property.change_status", "/fi-properties/{reference_code}/change-status"
    )

    config.add_route("fi_property_type.list", "/fi-property-types")
    config.add_route("fi_area.list", "/fi-areas")
    config.add_route("fi_area.search", "/fi-areas/search")
    config.add_route("fi_property.validation", "/fi-properties/{id}/validation")

    config.add_route("documents.get_preview", "/documents/{id}/preview")
    config.add_route("documents.get_pdf", "/documents/{id}/pdf")

    # document signing
    config.add_route("document_signing.add_signer", "/document-signing/{id}/add-signer")
    config.add_route(
        "document_signing.delete_signer",
        "/document-signing/{id}/delete-signer/{signer_id}",
    )

    config.add_route("brokerage_offer.generate", "/brokerage-offer/generate")

    # fi sales agreement
    config.add_route(
        "fi_sales_agreement.send_for_signing",
        "/fi-sales-agreement/{id}/send-for-signing",
    )
    config.add_route(
        "fi_sales_agreement.fetch_signing_status",
        "/fi-sales-agreement/{id}/fetch-signing-status",
    )
    config.add_route(
        "fi_sales_agreement.remind_signers",
        "/fi-sales-agreement/{id}/remind-signers",
    )
    config.add_route("fi_sales_agreement.list_create", "/fi-sales-agreement")
    config.add_route("fi_sales_agreement.read_edit", "/fi-sales-agreement/{id}")

    # purchase offer
    config.add_route("fi_purchase_offer.list_create", "/fi-purchase-offer")
    config.add_route("fi_purchase_offer.read_edit", "/fi-purchase-offer/{id}")
    config.add_route(
        "fi_purchase_offer.send_for_signing",
        "/fi-purchase-offer/{id}/send-for-signing",
    )
    config.add_route(
        "fi_purchase_offer.fetch_signing_status",
        "/fi-purchase-offer/{id}/fetch-signing-status",
    )
    config.add_route(
        "fi_purchase_offer.remind_signers",
        "/fi-purchase-offer/{id}/remind-signers",
    )

    config.add_route("fi_counter_offer.list_create", "/fi-counter-offer")
    config.add_route("fi_counter_offer.read_edit", "/fi-counter-offer/{id}")
    config.add_route(
        "fi_counter_offer.send_for_signing",
        "/fi-counter-offer/{id}/send-for-signing",
    )
    config.add_route(
        "fi_counter_offer.fetch_signing_status",
        "/fi-counter-offer/{id}/fetch-signing-status",
    )
    config.add_route(
        "fi_counter_offer.remind_signers",
        "/fi-counter-offer/{id}/remind-signers",
    )

    # fi housing company
    config.add_route("fi_housing_company.list_create", "/fi-housing-companies")

    # fi plot
    config.add_route("fi_plot_overview.list_create", "/fi-plot-overviews")

    # offer
    config.add_route("offer.create", "/offer")
    config.add_route("offer.list", "/offer/list/{reference_code}")
    config.add_route("offer.remove", "/offer/{id}")
    config.add_route("offer.reject", "/offer/{id}/reject")
    config.add_route(
        "offer.upload_signed_document", "/offer/upload-signed-document/{document_id}"
    )
    config.add_route(
        "offer.document_info",
        "/offer/document-info/{reference_code}",
    )

    # damage
    config.add_route(
        "damage.upload_attachments", "/damage/{property_id}/upload-attachments"
    )

    # archive
    config.add_route("property.archive", "/properties/{reference_code}/archive")

    # details of sale
    config.add_route("details_of_sale.list", "/details-of-sale/list")
    config.add_route("details_of_sale.create", "/details-of-sale")
    config.add_route(
        "details_of_sale.upload_attachments", "/details-of-sale/{id}/upload-attachments"
    )
    config.add_route(
        "details_of_sale.remove_attachments", "/details-of-sale/{id}/remove-attachments"
    )
    config.add_route(
        "details_of_sale.rename_attachments", "/details-of-sale/rename-attachments"
    )
    config.add_route(
        "details_of_sale.agent.upload_invoice",
        "/details-of-sale/{id}/agent/upload-invoice",
    )
    config.add_route("details_of_sale.retrieve", "/details-of-sale/{id}")
    config.add_route("details_of_sale.status", "/details-of-sale/{id}/status")
    config.add_route(
        "details_of_sale.request_changes", "/details-of-sale/{id}/request-changes"
    )
    config.add_route("details_of_sale.download", "/details-of-sale/{id}/download")
    config.add_route("details_of_sale.approve", "/details-of-sale/{id}/approve")
    config.add_route(
        "details_of_sale.download_attachments", "/details-of-sale/{id}/attachments"
    )
    config.add_route("details_of_sale.transaction", "/transactions")
    config.add_route("details_of_sale.report_kpi", "/report-kpi")

    # images
    config.add_route(
        "image.crud",
        f"{single_property_route}/images",
    )
    config.add_route(
        "image.generate_presigned_urls",
        f"{single_property_route}/images/presign",
    )
    config.add_route(
        "image.download_zip",
        f"{single_property_route}/images/download-zip",
    )

    # files
    config.add_route(
        "file.crud",
        f"{single_property_route}/files",
    )
    config.add_route(
        "file.generate_presigned_urls",
        f"{single_property_route}/files/presign",
    )
    config.add_route(
        "file.get",
        "/properties/{reference_code}/files/{file_id}",
    )

    # mapping
    config.add_route("mapping.list_features", "/features")
    config.add_route("mapping.list_cities", "/cities")
    config.add_route("mapping.list_sub_areas", "/sub-areas")
    config.add_route("mapping.list_conditions", "/conditions")
    config.add_route("mapping.list_settings", "/settings")
    config.add_route("mapping.list_views", "/views")
    config.add_route("mapping.list_orientations", "/orientations")
    config.add_route("mapping.list_property_types", "/property-types")
    config.add_route("mapping.list_nested_areas", "/nested-areas")
    config.add_route("mapping.list_all_nested_areas", "/nested-areas-all")
    config.add_route(
        "mapping.read_nested_area", "/nested-area-by-arealevel1id/{area_level_1_id}"
    )
    config.add_route("mapping.list_listing_types", "/property-listing-types")
    config.add_route("mapping.list_garage_types", "/property-garage-types")
    config.add_route("mapping.list_pool_types", "/property-pool-types")
    config.add_route("mapping.list_garden_types", "/property-garden-types")
    config.add_route("mapping.list_fi_property_types", "/fi-property-types")
    config.add_route("mapping.list_banks", "/banks")

    # sowise
    config.add_route("sowise.create", "/sowise/create")
    config.add_route("sowise.create_sales_agreement", "/sowise/create-sales-agreement")
    config.add_route(
        "sowise.upload_signed_sales_agreement",
        "/sowise/upload-signed-sales-agreement/{document_id}",
    )
    config.add_route("sowise.fill", "/sowise/metadata/{document_id}")
    config.add_route("sowise.download", "/sowise/download/{document_id}")
    config.add_route("sowise.content", "/sowise/content/{document_id}")
    config.add_route("sowise.delete", "/sowise/delete/{document_id}")
    config.add_route("sowise.add_signee", "/sowise/add-signee/{document_id}")
    config.add_route("sowise.send_to_sign", "/sowise/send-to-sign/{document_id}")
    config.add_route(
        "sowise.check_signing_status", "/sowise/check-signing-status/{type}/{id}"
    )
    config.add_route("sowise.stats", "/sowise/document-stats")
    config.add_route("sowise.upload", "/sowise/upload")

    # sales agreement
    config.add_route(
        "sales_agreement.document_info",
        "/sales-agreement/document-info/{reference_code}",
    )

    # Public ES (currently only for Strand website)
    # TODO: remove
    config.add_route("strandwebsite.list_all_properties", "/strandwebsite/properties")
    config.add_route("strandwebsite.read_property", "/strandwebsite/{reference_code}")

    config.add_route("public.es_property.list", "/public/es/properties")
    config.add_route(
        "public.es_property.read", "/public/es/properties/{reference_code}"
    )

    # TODO: remove
    config.add_route("strandwebsite.user_list_old", "/strandwebsite/users")

    config.add_route("public.realtor.list", "/public/{market}/realtors")
    config.add_route("public.realtor.read", "/public/{market}/realtors/{slug}")

    config.add_route("public.es_area.list", "/public/es/areas/")

    # Rightmove
    config.add_route(
        "integrations.rightmove.post_property",
        "/integrations/rightmove/post_property/{property_id}",
    )
    config.add_route(
        "integrations.rightmove.remove_property",
        "/integrations/rightmove/remove_property/{property_id}",
    )
    config.add_route(
        "integrations.rightmove.get_properties",
        "/integrations/rightmove/get_properties",
    )

    # InmobilienScout24
    config.add_route(
        "integrations.inmobilienscout24.post_property",
        "/integrations/inmobilienscout24/post_property/{property_id}",
    )
    config.add_route(
        "integrations.inmobilienscout24.read_property",
        "/integrations/inmobilienscout24/read_property/{property_id}",
    )
    config.add_route(
        "integrations.inmobilienscout24.update_property",
        "/integrations/inmobilienscout24/update_property/{property_id}",
    )
    config.add_route(
        "integrations.inmobilienscout24.remove_property",
        "/integrations/inmobilienscout24/remove_property/{property_id}",
    )

    # Fotocasa
    config.add_route(
        "integrations.fotocasa.post_property",
        "/integrations/fotocasa/post_property/{property_id}",
    )
    config.add_route(
        "integrations.fotocasa.update_property",
        "/integrations/fotocasa/update_property/{property_id}",
    )
    config.add_route(
        "integrations.fotocasa.remove_property",
        "/integrations/fotocasa/remove_property/{property_id}",
    )

    # Oikotie
    config.add_route(
        "integrations.oikotie.post_property",
        "/integrations/oikotie/post_property/{property_id}",
    )
    config.add_route(
        "integrations.oikotie.remove_property",
        "/integrations/oikotie/remove_property/{property_id}",
    )

    # Tags
    config.add_route("tag.list_create", "/tags")

    # Offices
    config.add_route("office.list_create", "/offices")

    # dashboard
    config.add_route("dashboad.activity", "/activity")
    config.add_route("admin.portalexport", "/admin/portalexport")

    # leads
    config.add_route("lead.list_create", "/leads")
    config.add_route("lead.list_pagination", "/leads-pagination")
    config.add_route("lead.read_edit", "/leads/{id}")

    # match making
    config.add_route("match_making.list_create", "/matchmaking")
    config.add_route("match_making.read_edit", "/matchmaking/{id}")
    config.add_route(
        "match_making.contact.delete",
        "/subscription/{subscription_id}",
    )
    config.add_route(
        "match_making.contact.email.delete",
        "/email-subscription/{contact_email}",
    )
    config.add_route("match_making.property.list_read", "/matchmaking/{id}/properties")
    config.add_route(
        "match_making.property.edit",
        "/matchmaking/{id}/properties/{property_id}",
    )
    config.add_route(
        "match_making.ms.email_list",
        "/email-list/{realtor_id}/contacts/{contact_id}",
    )
    config.add_route("match_making.ms.email_create", "/email-create")
    config.add_route(
        "match_making.mailgun.email_insights",
        "/email-insights/{match_making_id}",
    )
    config.add_route("match_making.mailgun.email_template.list", "/email-templates")
    config.add_route(
        "match_making.track.create_edit",
        "/matchmaking/{id}/properties/{property_reference}/realtor/{realtor_id}/track",
    )
    config.add_route(
        "match_making.read_edit.email_auto_sent",
        "/matchmaking/{id}/email-triggered",
    )
    config.add_route("match_making.newsletter_subscribe", "/matchmaking-subscribe")

    # events
    config.add_route("event.list_create", "/events")
    config.add_route("event.read_edit", "/events/{id}")

    # dias
    config.add_route("dias.list_create_kiinteistokauppa", "/dias-kiinteistokauppa")
    config.add_route(
        "dias.list_create_vuokraoikeuden_siirto", "/dias-vuokraoikeuden-siirto"
    )
    config.add_route("dias.edit_kiinteistokauppa", "/dias-kiinteistokauppa/{id}")
    config.add_route("dias.read_edit", "/dias/{id}")
    config.add_route("dias.config", "/dias-config")

    # Dias shared trade
    config.add_route("dias.list_create_shared_trade", "/dias-shared-trade")
    config.add_route("dias.read_edit_shared_trade", "/dias-shared-trade/{id}")
    config.add_route("dias.initiate_shared_trade", "/dias-shared-trade/initiate/{id}")
    config.add_route("dias.list_banks", "/dias-banks")
    config.add_route(
        "dias.start_signing_shared_trade",
        "/dias-initiated-shared-trade/{trade_id}/start-signing",
    )
    config.add_route(
        "dias.cancel_shared_trade",
        "/dias-initiated-shared-trade/{trade_id}/cancel",
    )
    config.add_route("dias.list_create_attachments", "/dias-attachments")
    config.add_route("dias.read_attachment", "/dias-attachments/{id}")
    config.add_route(
        "dias.generate_presigned_attachment_urls",
        "/dias-attachments/{reference_code}/presign",
    )

    # groups
    config.add_route("group.list_create", "/groups")
    config.add_route("group.read_edit", "/groups/{id}")

    # Finnish property public API
    config.add_route(
        "public.fi_property.read",
        "/public/properties/fi/{reference_code}/{language}",
    )
    # lead manual
    config.add_route("manual_assigned_lead.list", "/manual-assigned-leads")
    config.add_route(
        "manual_assigned_lead.allocate", "/manual-assigned-leads/{id}/allocate"
    )
    config.add_route(
        "manual_assigned_lead.reallocate", "/manual-assigned-leads/{id}/reallocate"
    )
    config.add_route(
        "manual_assigned_lead.toggle_discarded",
        "/manual-assigned-leads/{id}/toggle-discarded",
    )

    # contracts
    config.add_route("contracts.list", "/contracts/list")

    # companies
    config.add_route("company.list_create", "/companies")
    config.add_route("company.read_edit", "/companies/{id}")

    # Expenses
    config.add_route("expense.create_expense_account", "/expenses/account")
    config.add_route("expense.create_list", "/expenses/transactions")
    config.add_route("expense.card_details", "/expenses/card-details")
    config.add_route("expense.disconnect_card", "/expenses/disconnect-card")

    # Advertisements
    config.add_route("advertisement.list_create", "/advertisements")

    # config.add_route("advertisement.list_preview", "/advertisements/{id}/preview")
    config.add_route("advertisement.read_edit_delete", "/advertisements/{id}")
    config.add_route("advertisement.publish", "/advertisements/publish/{id}")
    config.add_route("advertisement.duplicate", "/advertisements/duplicate/{id}")
    config.add_route("advertisement.end", "/advertisements/end/{id}")
    config.add_route("advertisement.public_read", "/advertisements/public/{id}")
    config.add_route(
        "advertisement.preview_settings", "/advertisements/{id}/preview-settings"
    )

    # Dokobit webhooks
    config.add_route(
        "dokobit_webhooks.signing_event", "/dokobit/webhooks/signing-event"
    )

    # Smartly
    config.add_route("smartly.csv", "/smartly")
    config.add_route("smartly.update_ads", "/smartly/update-ads")

    # Organization
    config.add_route(
        "organization.ad_template.list_create", "/organizations/ad-templates"
    )
    config.add_route(
        "organization.ad_template.read_edit_delete",
        "/organizations/ad-templates/{template_id}",
    )

    config.add_route("fi_details_of_sale.list_create", "/fi-details-of-sale")
    config.add_route("fi_details_of_sale.read_edit", "/fi-details-of-sale/{id}")
    config.add_route(
        "fi_details_of_sale.update_status", "/fi-details-of-sale/{id}/update-status"
    )
    config.add_route(
        "fi_details_of_sale.prefill", "/fi-details-of-sale/prefill/{property_id}"
    )

    # Meta
    config.add_route("meta.csv", "/meta-csv")

    # FI Sales Data
    config.add_route("fi_sales_data.read", "/fi-sales-data")
