from datetime import datetime
from typing import Optional, Union

from strandproperties.constants import EventLogAction, EventLogObjectType
from strandproperties.schemas.base import BaseSchema, PaginatedList


class EventLogActivities(BaseSchema):
    id: Optional[int] = None
    object_type: Optional[EventLogObjectType] = None
    object_id: Optional[Union[int, str]] = None
    actor_id: Optional[int] = None
    actor_name: Optional[str] = None
    action: Optional[EventLogAction] = None
    data_before: Optional[dict] = None
    data_after: Optional[dict] = None
    details: Optional[dict] = None
    message: Optional[str] = None
    created_at: Optional[datetime] = None


class EventLogCreate(BaseSchema):
    object_type: EventLogObjectType
    object_id: int
    action: EventLogAction
    data_before: Optional[dict] = None
    data_after: Optional[dict] = None
    details: Optional[dict] = None
    relation_object_type: Optional[EventLogObjectType] = None
    relation_object_id: Optional[int] = None


class EventLogResponse(BaseSchema):
    id: int
    object_type: EventLogObjectType
    object_id: int
    actor_id: int
    action: EventLogAction
    data_before: Optional[dict] = None
    data_after: Optional[dict] = None
    details: Optional[dict] = None
    relation_object_type: Optional[EventLogObjectType] = None
    relation_object_id: Optional[int] = None
