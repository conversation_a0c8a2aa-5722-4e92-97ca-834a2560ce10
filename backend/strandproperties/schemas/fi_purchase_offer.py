from datetime import date, datetime
from typing import List, Optional

from strandproperties.constants import (
    FIPurchaseOfferDigitalPurchaseExpenses,
    FIPurchaseOfferDownPaymentTerm,
    FIPurchaseOfferPaymentMethod,
    FIPurchaseOfferRightOfUseTransfer,
    FIPurchaseOfferStatusEnum,
)
from strandproperties.schemas.base import BaseGenericSchema, BaseSchema
from strandproperties.schemas.contact import ContactRead
from strandproperties.schemas.document_signing import DocumentSigningRead, Signer
from strandproperties.schemas.fi_counter_offer import FICounterOfferRead
from strandproperties.schemas.fi_property.fi_property import FIPropertyRead
from strandproperties.views.exceptions import CustomValidationError


class FIPurchaseOfferReviewedDocument(BaseSchema):
    document_name: str
    review_date: Optional[str] = None


class FIPurchaseOfferTermOfSale(BaseSchema):
    term_name: str
    deadline_date: Optional[str] = None


class FIPurchaseOfferSign(BaseSchema):
    signers: List[Signer]
    comment: Optional[str] = None
    last_signing_date: str
    language: Optional[str] = "fi"


class _FIPurchaseOfferBase(BaseSchema):
    status: Optional[FIPurchaseOfferStatusEnum] = None
    property_id: int
    unencumbered_price: Optional[float] = None
    loan_amount: Optional[float] = None
    price_including_loan: Optional[float] = None
    loan_date: Optional[date] = None
    building_manager_certificate_date: Optional[date] = None
    payment_method: Optional[FIPurchaseOfferPaymentMethod] = None
    payment_schedule_and_terms: Optional[str] = None
    transfer_right_of_use: Optional[FIPurchaseOfferRightOfUseTransfer] = None
    transfer_right_of_use_latest: Optional[datetime] = None
    availability_delay_fee: Optional[float] = None
    delay_fee_per_started_week: Optional[float] = None
    is_rented: Optional[bool] = None
    buyer_receives_rent_starting: Optional[date] = None
    buyer_receives_rental_deposit_latest: Optional[date] = None
    buyer_responsible_for_costs_starting: Optional[date] = None
    buyer_responsible_for_capital_expenditure_charge_starting: Optional[date] = None
    digital_purchase: Optional[bool] = None
    digital_purchase_expenses: Optional[FIPurchaseOfferDigitalPurchaseExpenses] = None
    digital_purchase_expenses_details: Optional[str] = None
    standard_compensation: Optional[float] = None
    down_payment: Optional[float] = None
    down_payment_term: Optional[FIPurchaseOfferDownPaymentTerm] = None
    reviewed_documents: Optional[List[FIPurchaseOfferReviewedDocument]] = None
    terms_of_sale: Optional[List[FIPurchaseOfferTermOfSale]] = None
    valid_until: Optional[datetime] = None
    signed_latest: Optional[datetime] = None
    accept_offer_email: Optional[str] = None
    accept_offer_phone: Optional[str] = None
    has_read_privacy_policy: Optional[bool] = None


class FIPurchaseOfferCreate(_FIPurchaseOfferBase):
    created_by: int
    buyer_ids: Optional[List[int]] = None
    seller_ids: Optional[List[int]] = None


class FIPurchaseOfferEdit(_FIPurchaseOfferBase):
    buyer_ids: Optional[List[int]] = None
    seller_ids: Optional[List[int]] = None


class FIPurchaseOfferRead(_FIPurchaseOfferBase):
    id: int
    created_at: datetime
    created_by: Optional[int] = None
    counter_offers: Optional[List[FICounterOfferRead]] = None
    property: Optional["FIPropertyRead"] = None
    sellers: Optional[List["ContactRead"]] = []
    buyers: Optional[List["ContactRead"]] = []
    signings: List["DocumentSigningRead"]


class FIPurchaseOfferList(BaseSchema):
    items: List[FIPurchaseOfferRead]


class FIPurchaseOfferParams(BaseGenericSchema):
    property_id: int


def validate_complete_schema(data: FIPurchaseOfferCreate):
    errors = {}
    required_fields = {
        "property_id": "Property ID",
        "unencumbered_price": "Unencumbered price",
        "payment_method": "Payment method",
        "transfer_right_of_use": "Transfer right of use",
        "digital_purchase": "Digital purchase",
        "valid_until": "Valid until",
        "signed_latest": "Signed latest",
        "accept_offer_email": "Accept offer email",
        "accept_offer_phone": "Accept offer phone",
    }

    for field, field_name in required_fields.items():
        if getattr(data, field) is None:
            errors[field] = {"message": f"{field_name} is required.", "code": "missing"}

    # conditional rules
    if (
        data.transfer_right_of_use == FIPurchaseOfferRightOfUseTransfer.LATER
        and data.transfer_right_of_use_latest is None
    ):
        errors["transfer_right_of_use_latest"] = {
            "message": "Transfer right of use latest is required when transfer right of use is LATER.",
            "code": "missing",
        }
    if data.is_rented:
        if data.buyer_receives_rent_starting is None:
            errors["buyer_receives_rent_starting"] = {
                "message": "Buyer receives rent starting is required when is rented is true.",
                "code": "missing",
            }
        if data.buyer_receives_rental_deposit_latest is None:
            errors["buyer_receives_rental_deposit_latest"] = {
                "message": "Buyer receives rental deposit latest is required when is rented is true.",
                "code": "missing",
            }
    if data.digital_purchase and data.digital_purchase_expenses is None:
        errors["digital_purchase_expenses"] = {
            "message": "Digital purchase expenses is required when digital purchase is true.",
            "code": "missing",
        }

    if errors:
        raise CustomValidationError(errors)
    return data
