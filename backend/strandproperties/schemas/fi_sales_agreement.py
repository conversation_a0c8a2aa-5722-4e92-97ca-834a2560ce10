from datetime import date, datetime
from typing import Optional, List
from strandproperties.views.exceptions import CustomValidationError
from enum import Enum

from strandproperties.schemas.base import BaseSchema, BaseGenericSchema
from strandproperties.schemas.fi_property.fi_property import FIPropertyRead

from strandproperties.constants import (
    FISalesAgreementPaymentTermsEnum,
    FISalesAgreementAcquisitionEnum,
    FISalesAgreementAvailabilityEnum,
    FISalesAgreementCommissionBasisEnum,
    FISalesAgreementCommissionTypeEnum,
    FISalesAgreementLeaseAgreementEnum,
    FISalesAgreementTaxConsequenceEnum,
    FISalesAgreementTermEnum,
    FISalesAgreementShareRegisterFormatEnum,
    FISalesAgreementStatusEnum,
)
from strandproperties.schemas.user import UserListRead
from strandproperties.schemas.contact import ContactListRead
from strandproperties.schemas.document_signing import DocumentSigningRead, Signer


class FISalesAgreementContactAcquisitionBase(BaseSchema):
    contact_id: int
    acquisition: Optional[FISalesAgreementAcquisitionEnum] = None
    seller_is_married_or_in_registered_relationship: Optional[bool] = None
    seller_has_been_married_or_in_registered_relationship: Optional[bool] = None
    seller_has_spouses_consent: Optional[bool] = None
    legal_partitioning_is_complete: Optional[bool] = None
    divorce_legally_binding: Optional[bool] = None
    acquisition_date: Optional[date] = None
    acquisition_cost: Optional[str] = None
    client_has_used_residence_as_residence: Optional[bool] = None
    residency_start_date: Optional[date] = None
    residency_end_date: Optional[date] = None
    share_register_format: Optional[FISalesAgreementShareRegisterFormatEnum] = None
    share_register_storage: Optional[str] = None
    tax_consequence: Optional[FISalesAgreementTaxConsequenceEnum] = None


class FISalesAgreementContactAcquisitionRead(FISalesAgreementContactAcquisitionBase):
    id: int


class _FISalesAgreementBase(BaseSchema):
    property_id: int
    status: Optional[FISalesAgreementStatusEnum] = None

    agreed_marketing_methods: Optional[str] = None
    separate_marketing_appendix: Optional[bool] = None

    unencumbered_price_request: Optional[float] = None  # Required
    unencumbered_price_request_estimate: Optional[float] = None  # Required
    shares_include_loan: Optional[bool] = None

    loan_amount: Optional[float] = None  # If shared_include_loan value is True Required
    loan_details: Optional[str] = None
    price_including_loan: Optional[float] = (
        None  # If shares_include_loan value is True Required
    )

    payment_terms: Optional[FISalesAgreementPaymentTermsEnum] = None
    payment_term_other: Optional[str] = None
    average_selling_time_estimate: Optional[str] = None
    factors_affecting_sales: Optional[str] = (
        None  # If payment_term_other value is other Required
    )

    availability: Optional[FISalesAgreementAvailabilityEnum] = None  # Required
    date_when_available: Optional[date] = None  # If availability value is date Required
    availability_details: Optional[str] = (
        None  # If availability value is other Required
    )
    tenant_name: Optional[str] = None  # If availability value is rented Required
    tenant_contact_details: Optional[str] = None
    lease_agreement: Optional[FISalesAgreementLeaseAgreementEnum] = (
        None  # If availability value is rented Required
    )
    lease_agreement_term: Optional[FISalesAgreementTermEnum] = (
        None  # If availability value is rented Required
    )

    lease_terminated: Optional[bool] = None  # If availability value is rented Required
    tenant_has_paid_rent_on_time: Optional[bool] = (
        None  # If availability value is rented Required
    )
    tenant_paying_rent_details: Optional[str] = None
    lease_amount: Optional[float] = None
    lease_deposit: Optional[float] = None
    lease_start_date: Optional[date] = None
    lease_end_date: Optional[date] = None
    lease_agreement_details: Optional[str] = None

    restrictive_right_of_user: Optional[bool] = None
    restrictive_right_of_user_details: Optional[str] = (
        None  # If restrictive_right_of_user value is True Required
    )

    written_consent_to_transfer: Optional[bool] = None
    ## TODO: Are we missing kirjallinen suostumus luovutukseen

    belongs_to_business_activities: Optional[bool] = None  # Required
    assignment_validity: Optional[FISalesAgreementTermEnum] = None  # Required
    assignment_validity_renewal_period: Optional[int] = (
        None  # If assignment_validity indefinite is True Required
    )
    start_date: Optional[date] = None  # Required
    end_date: Optional[date] = None  # Required

    commission_basis_code: Optional[FISalesAgreementCommissionBasisEnum] = (
        None  # Required
    )
    commission_type: Optional[FISalesAgreementCommissionTypeEnum] = None  # Required
    vat: Optional[bool] = None  # Required
    commission_fixed: Optional[float] = (
        None  # If commission_type value is fixed Required
    )
    commission_other: Optional[str] = None
    commission_percentage: Optional[float] = (
        None  # If commission_type value is percentage Required
    )
    commission_details: Optional[str] = None
    other_expenses_details: Optional[str] = None
    expense_if_no_commission: Optional[str] = None

    unpaid_maintenance_charge: Optional[bool] = None
    unpaid_maintenance_charge_amount: Optional[float] = (
        None  # If unpaid_maintenance_charge is True Required
    )

    digital_trading_allowed: Optional[bool] = None
    is_domestic_sale: Optional[bool] = None  # Required
    start_assignment_immediately: Optional[bool] = None  # If is_domestic_sale is True
    start_marketing_after_cancel_period: Optional[bool] = (
        None  # If is_domestic_sale is True
    )
    customer_asked_to_read_privacy_policy: Optional[bool] = None
    previous_external_sales_agreement: Optional[bool] = None
    previous_external_sales_agreement_details: Optional[str] = None

    additional_details: Optional[str] = None


class FISalesAgreementCreate(_FISalesAgreementBase):
    realtor_user_ids: list[int]
    contact_ids: list[int]
    consenter_ids: list[int]
    created_by: int
    contact_acquisitions: Optional[List[FISalesAgreementContactAcquisitionBase]]


class FISalesAgreementEdit(_FISalesAgreementBase):
    realtor_user_ids: list[int]
    contact_ids: list[int]
    consenter_ids: list[int]
    contact_acquisitions: Optional[List[FISalesAgreementContactAcquisitionBase]]


class FISalesAgreementRead(_FISalesAgreementBase):
    id: int
    created_by: Optional[int] = None
    created_at: datetime
    property: FIPropertyRead
    realtor_users: list[UserListRead]
    contacts: list[ContactListRead]
    consenters: list[ContactListRead]
    signings: list[DocumentSigningRead]
    contact_acquisitions: List[FISalesAgreementContactAcquisitionRead]


class FISalesAgreementParams(BaseGenericSchema):
    property_id: int


def validate_complete_schema(data: _FISalesAgreementBase):
    """
    Validates the final submission of the _FISalesAgreementBase schema.

    Args:
        data (_FISalesAgreementBase): The input model instance.

    Returns:
        _FISalesAgreementBase: The validated instance.

    Raises:
        CustomValidationError: If validation fails, raises a custom error with formatted messages.
    """
    errors = {}

    required_fields = {
        "property_id": "Property ID",
        "unencumbered_price_request": "Unencumbered price request",
        "unencumbered_price_request_estimate": "Unencumbered price request estimate",
        "shares_include_loan": "Shares include loan",
        "payment_terms": "Payment terms",
        "availability": "Availability",
        "belongs_to_business_activities": "Belongs to business activities",
        "assignment_validity": "Assignment validity",
        "start_date": "Start date",
        "end_date": "End date",
        "commission_basis_code": "Commission basis code",
        "commission_type": "Commission type",
        "vat": "VAT",
        "is_domestic_sale": "Is domestic sale",
    }

    # Ensure required fields are present
    for field, field_name in required_fields.items():
        if getattr(data, field) is None:
            errors[field] = {"message": f"{field_name} is required.", "code": "missing"}

    # Conditional rules
    if data.shares_include_loan is True and data.loan_amount is None:
        errors["loan_amount"] = {
            "message": "Loan amount is required if shares_include_loan is True.",
            "code": "missing",
        }

    if (
        data.payment_terms == FISalesAgreementPaymentTermsEnum.OTHER
        and not data.payment_term_other
    ):
        errors["payment_term_other"] = {
            "message": "Payment term other is required if payment_terms is 'other'.",
            "code": "missing",
        }

    if (
        data.availability == FISalesAgreementAvailabilityEnum.DATE
        and not data.date_when_available
    ):
        errors["date_when_available"] = {
            "message": "Date when available is required if availability is 'date'.",
            "code": "missing",
        }

    if data.availability == FISalesAgreementAvailabilityEnum.RENTED:
        required_rented_fields = {
            "tenant_name": "Tenant name",
            "lease_agreement": "Lease agreement",
            "lease_agreement_term": "Lease agreement term",
            "lease_terminated": "Lease terminated",
            "tenant_has_paid_rent_on_time": "Tenant has paid rent on time",
        }
        for field, field_name in required_rented_fields.items():
            if getattr(data, field) is None:
                errors[field] = {
                    "message": f"{field_name} is required if availability is 'rented'.",
                    "code": "missing",
                }

    if (
        data.restrictive_right_of_user is True
        and not data.restrictive_right_of_user_details
    ):
        errors["restrictive_right_of_user_details"] = {
            "message": "Restrictive right of user details is required if restrictive_right_of_user is True.",
            "code": "missing",
        }

    if (
        data.assignment_validity == FISalesAgreementTermEnum.INDEFINITE
        and not data.assignment_validity_renewal_period
    ):
        errors["assignment_validity_renewal_period"] = {
            "message": "Assignment validity renewal period is required if assignment_validity is 'indefinite'.",
            "code": "missing",
        }

    if (
        data.commission_type == FISalesAgreementCommissionTypeEnum.PERCENTAGE
        and data.commission_percentage is None
    ):
        errors["commission_percentage"] = {
            "message": "Commission percentage is required if commission_type is 'percentage'.",
            "code": "missing",
        }

    if (
        data.commission_type == FISalesAgreementCommissionTypeEnum.FIXED
        and data.commission_fixed is None
    ):
        errors["commission_fixed"] = {
            "message": "Commission fixed is required if commission_type is 'fixed'.",
            "code": "missing",
        }

    # TODO: the schema changed double check if we still want this
    # if (
    #     data.client_has_used_residence_as_residence is True
    #     and data.acquisition_date is None
    # ):
    #     errors["acquisition_date"] = {
    #         "message": "Acquisition date is required if client_has_used_residence_as_residence is true.",
    #         "code": "missing",
    #     }

    if errors:
        raise CustomValidationError(errors)

    return data


class FISalesAgreementList(BaseSchema):
    items: List[FISalesAgreementRead]


class FISalesAgreementSign(BaseSchema):
    signers: List[Signer]
    comment: Optional[str] = None
    last_signing_date: str
    language: Optional[str] = "fi"
