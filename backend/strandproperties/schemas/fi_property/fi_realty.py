from enum import StrEnum
from typing import Optional

from strandproperties.constants import DEFAULT_CURRENCY, Status
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.fi_property.fi_address import (
    FIAddressCreate,
    FIAddressRead,
)
from strandproperties.schemas.fi_property.fi_common import (
    FIChoiceEnum,
    FITranslatedText,
)


class FIRoleCodeEnum(StrEnum):
    """
    Defines the role of the business user. Possible values are:

    MARKETING_AGENT. Agent represented in announcements which are published based on the realty.
    RESPONSIBLE_AGENT. Agent who is responsible of the assignment and thus responsible of the realty.
    SHOWING_AGENT. Agent who is taking care of a specific showing.
    """

    MARKETING_AGENT = "MARKETING_AGENT"
    RESPONSIBLE_AGENT = "RESPONSIBLE_AGENT"
    SHOWING_ALERT = "SHOWING_ALERT"


class FIStatusCodeEnum(StrEnum):
    """
    Specifies if the specified business user is currently acting as an agent. Possible values are:

    ACTIVE. The business user is currently acting as an agent.
    PASSIVE. The business user has acted as an agent at the past.
    """

    ACTIVE = "ACTIVE"
    PASSIVE = "PASSIVE"


class FIConsentDeliveryMethodCodeEnum(StrEnum):
    """
    Defines the method how the consent was delivered. Possible values are:

    DELIVERED_BY_EMAIL. The consent was delivered by email.
    DELIVERED_VERBALLY. The consent was delivered verbally e.g. on the phone or by meetign the principal.
    DELIVERED_BY_OTHER_METHOD. The consent was delivered by other means, e.g. through private messaging.
    """

    DELIVERY_BY_EMAIL = "DELIVERY_BY_EMAIL"
    DELIVERY_VERBALLY = "DELIVERY_VERBALLY"
    DELIVERY_BY_OTHER_METHOD = "DELIVERY_BY_OTHER_METHOD"


class FICostTypeCodeEnum(StrEnum):
    """
    Identifies the type of the periodic charge. Possible values are:

    ALERT_CONTROL_SYSTEM. Fee for a system designed to detect intrusion, such as
    unauthorized entry, into a building or other areas such as a home.
    CLEANING. Fee for cleaning and sanitation management.
    ELECTRIC_CAR_CHARGING_POINT. Fee for electric car charging point.
    ELECTRIC_HEATING_COSTS. Costs for electrical heating.
    GARAGE. Fee for a garage.
    OTHER_HEATING_COSTS. Costs for other heating.
    OUTDOOR_PARKING_SPACE. Fee for a parking space that's outside and has no shelter.
    OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG. Fee for a parking space that's
    outside with no shelter and has an electrical plug.
    PARKING_SPACE_FEE. General fee for a parking space.
    PROPERTY_TAX. Annual property tax based on community or city tax rates.
    ROAD. Fee for road usage.
    SAUNA. Fee for usage of sauna.
    SPACE_IN_CARPORT. Fee for a car parking in a carport.
    SPACE_IN_CARPORT_WITH_ELECTRICAL_PLUG. Fee for a car parking in a carport with a electrical plug.
    SPACE_IN_PARKING_HALL. Fee for a parking space in a carpark or parking hall.
    TELECOM_CONNECTION. Telecom connection fee.
    TV. TV fee.
    USE_OF_HOUSE_DRYING_ROOM. Drying room fee.
    USE_OF_HOUSE_LAUNDRY_ROOM. Laundry room fee.
    WASTE. Fee for waste management.
    WATER. Fee for usage of water.
    WATER_AND_WASTE. Fee for water and wastewater.
    """

    ALERT_CONTROL_SYSTEM = "ALERT_CONTROL_SYSTEM"
    CLEANING = "CLEANING"
    ELECTRIC_CAR_CHARGING_POINT = "ELECTRIC_CAR_CHARGING_POINT"
    ELECTRIC_HEATING_COSTS = "ELECTRIC_HEATING_COSTS"
    GARAGE = "GARAGE"
    OTHER_HEATING_COSTS = "OTHER_HEATING_COSTS"
    OUTDOOR_PARKING_SPACE = "OUTDOOR_PARKING_SPACE"
    OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG = (
        "OUTDOOR_PARKING_SPACE_WITH_ELECTRICAL_PLUG"
    )
    PARKING_SPACE_FEE = "PARKING_SPACE_FEE"
    PROPERTY_TAX = "PROPERTY_TAX"
    ROAD = "ROAD"
    SAUNA = "SAUNA"
    SPACE_IN_CARPORT = "SPACE_IN_CARPORT"
    SPACE_IN_CARPORT_WITH_ELECTRICAL_PLUG = "SPACE_IN_CARPORT_WITH_ELECTRICAL_PLUG"
    SPACE_IN_PARKING_HALL = "SPACE_IN_PARKING_HALL"
    TELECOM_CONNECTION = "TELECOM_CONNECTION"
    TV = "TV"
    USE_OF_HOUSE_DRYING_ROOM = "USE_OF_HOUSE_DRYING_ROOM"
    USE_OF_HOUSE_LAUNDRY_ROOM = "USE_OF_HOUSE_LAUNDRY_ROOM"
    WASTE = "WASTE"
    WATER = "WATER"
    WATER_AND_WASTE = "WATER_AND_WASTE"


class FIWaterChargeTypeCodeEnum(StrEnum):
    """
    Identifies the type of the water charge. The value of this attribute must be
    set only when the value of the typeCode attribute is WATER. Possible values
    are:

    BASIC_FEE. Water cost with fixed basic fee.
    BY_USE. Water fee based on consumption.
    BY_USE_WITH_ADVANCE. Water fee based on consumption, but payment must be
    done in advance and then it is periodically checked and compensated if
    needed.
    INCLUDED_IN_MAINTENACE_CHARGE. Water fee that is included in the maintenance
    charge and there is no separate fee for it.
    INCLUDED_IN_RENT. Water fee that is included in the rent and there is no separate fee for it.
    """

    BASIC_FEE = "BASIC_FEE"
    BY_USE = "BY_USE"
    BY_USE_WITH_ADVANCE = "BY_USE_WITH_ADVANCE"
    INCLUDED_IN_MAINTENACE_CHARGE = "INCLUDED_IN_MAINTENACE_CHARGE"
    INCLUDED_IN_RENT = "INCLUDED_IN_RENT"


class FIChargePeriodCodeEnum(StrEnum):
    """
    Identifies how often the periodic charge is paid. Possible values are:

    NOT_KNOWN. The charge period is not know. If the user hasn't selected the
    charge period, you must send this value to the API.
    MONTH. The periodic charge is paid once per month.
    MONTH_PER_PERSON. Each person living in the realty (or using it) must pay the periodic charge once per month.
    SINGLE_PAYMENT. The charge is paid only once.
    YEAR. The periodic charge is paid onnce per year.
    OTHER. None of the above.
    """

    NOT_KNOWN = "NOT_KNOWN"
    MONTH = "MONTH"
    MONTH_PER_PERSON = "MONTH_PER_PERSON"
    SINGLE_PAYMENT = "SINGLE_PAYMENT"
    YEAR = "YEAR"
    OTHER = "OTHER"


class FIRealtyAvailabilityCodeEnum(StrEnum):
    """
    Identifies when the realty is available. A realty is considered as available
    when you can move in (realty is an apartment) or start using it (realty
    isn't an apartment). Possible values are:

    AVAILABLE. Realty is available immediately.
    NEGOTIABLE. The availability date is negotiable.
    DATE. Realty is available at the specified date. When you use this value,
    you must set the value of the date attribute.
    RENTED. Realty will be sold rented.
    OTHER. None of the above
    """

    AVAILABLE = "AVAILABLE"
    NEGOTIABLE = "NEGOTIABLE"
    DATE = "DATE"
    RENTED = "RENTED"
    OTHER = "OTHER"


class FIConditionCodeEnum(StrEnum):
    """
    Describes the condition of the realty. Possible values are:

    NEW. Realty is new and no one has lived in it or used it in any way.
    GOOD. Realty is modern and in good condition.
    SATISFACTORY. Realty is livable but it requires a renovation.
    TOLERABLE. Realty needs a renovation before you can live in it.
    UNCLASSIFIED. The condition isn't specified. Realty isn't shown on the
    search results if the condition filter is used.
    """

    NEW = "NEW"
    GOOD = "GOOD"
    SATISFACTORY = "SATISFACTORY"
    TOLERABLE = "TOLERABLE"
    UNCLASSIFIED = "UNCLASSIFIED"


class FIAdditionalRealtyDetailLinkTypeCodeEnum(StrEnum):
    VIRTUAL_PRESENTATION = "VIRTUAL_PRESENTATION"
    VIDEO_PRESENTATION = "VIDEO_PRESENTATION"
    OTHER = "OTHER"


class FIDamageTypeCodeEnum(StrEnum):
    WATER_DAMAGE = "WATER_DAMAGE"
    MOISTURE_DAMAGE = "MOISTURE_DAMAGE"
    OTHER = "OTHER"


class FILivingComfortFactorCodeEnum(StrEnum):
    NOISE_SMELL_VIBRATION_ETC = "NOISE_SMELL_VIBRATION_ETC"
    HEAT_COLD_DRAUGHT_ETC = "HEAT_COLD_DRAUGHT_ETC"
    PESTS_RODENTS_ETC = "PESTS_RODENTS_ETC"
    FUNCTIONAL_OR_TECHNICAL_DEFECTS = "FUNCTIONAL_OR_TECHNICAL_DEFECTS"
    OTHER = "OTHER"


class FIKeyUsageCodeEnum(StrEnum):
    SAFETY_LOCK = "SAFETY_LOCK"
    DOOR_LOCK = "DOOR_LOCK"
    PRIMARY_LOCK = "PRIMARY_LOCK"
    OTHER_LOCK = "OTHER_LOCK"


class FIKeyTypeCodeEnum(StrEnum):
    MECHANICAL_KEY = "MECHANICAL_KEY"
    DIGITAL_KEY = "DIGITAL_KEY"
    OTHER = "OTHER"


class FILeaseTypeCodeEnum(StrEnum):
    FIXED_TERM = "FIXED_TERM"
    UNTIL_FURTHER_NOTICE = "UNTIL_FURTHER_NOTICE"


class FILivingFormTypeCodeEnum(StrEnum):
    NONSUBSIDISED = "NONSUBSIDISED"
    INTEREST_SUBSIDIED = "INTEREST_SUBSIDIED"
    SHARED_APARTMENT = "SHARED_APARTMENT"
    SUBTENANCY = "SUBTENANCY"
    SENIOR_HOUSE = "SENIOR_HOUSE"
    SERVICE_HOUSE = "SERVICE_HOUSE"
    EMPLOYMENT_BENEFIT_APARTMENT = "EMPLOYMENT_BENEFIT_APARTMENT"
    STUDENT_APARTMENT = "STUDENT_APARTMENT"


class FIAgent(BaseSchema):
    # A unique id which identifies the business user who acts as an agent.
    # business_user_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    profile_picture_url: Optional[str] = None
    role_code: FIRoleCodeEnum
    status_code: FIStatusCodeEnum


class FIConsentToChange(BaseSchema):
    # Date in the ISO 8601 format.
    date: str
    # Indicates whether the principal has provided the consent to change details for this assignment.
    consent_provided: bool
    consent_delivery_method_code: FIConsentDeliveryMethodCodeEnum


class FICost(BaseSchema):
    # The amount of one payment.
    amount: Optional[float] = None
    currency_code: Optional[str] = None
    description: Optional[list[FITranslatedText]] = None
    type_code: Optional[FICostTypeCodeEnum] = None
    water_charge_type_code: Optional[FIWaterChargeTypeCodeEnum] = None
    charge_period_code: Optional[FIChargePeriodCodeEnum] = None


class FIRealtyAvailability(BaseSchema):
    code: Optional[FIRealtyAvailabilityCodeEnum] = None
    date: Optional[str] = None
    description: Optional[list[FITranslatedText]] = None


class FIRealtyCondition(BaseSchema):
    code: Optional[FIConditionCodeEnum] = None
    description: Optional[list[FITranslatedText]] = None


class FIRealtyShare(BaseSchema):
    digital_share_group_identifiers: Optional[list[str]] = None
    list_of_shares_transferred: Optional[FIChoiceEnum] = None
    share_certificate_available: Optional[FIChoiceEnum] = None


class FIAdditionalRealtyDetailLink(BaseSchema):
    title: Optional[list[FITranslatedText]] = None
    url: Optional[str] = None
    type_code: Optional[FIAdditionalRealtyDetailLinkTypeCodeEnum] = None


class FIRealtyAdditionalInformation(BaseSchema):
    activities_and_recreation_description: Optional[list[FITranslatedText]] = []
    additional_description: Optional[list[FITranslatedText]] = []
    additional_realty_detail_links: Optional[list[FIAdditionalRealtyDetailLink]] = []
    driving_instructions: Optional[list[FITranslatedText]] = []
    nearby_amenities_description: Optional[list[FITranslatedText]] = []
    transportation_services_description: Optional[list[FITranslatedText]] = []
    school_childcare_description: Optional[list[FITranslatedText]] = []


class FISupplierAssignedIdentifiers(BaseSchema):
    supplier_assigned_id: str
    scheme_id: str


class DamageAttachment(BaseSchema):
    id: int
    name: str
    url: str
    is_new_name: Optional[bool] = None


class FIDamages(BaseSchema):
    damages_exists_code: Optional[FIChoiceEnum] = None
    damage_type_code: FIDamageTypeCodeEnum
    # This is mandatory, if the damageTypeCode is YES.
    damage_date: Optional[str] = None
    # This is mandatory, if the damageTypeCode is YES.
    cause_description: Optional[list[FITranslatedText]] = []
    # This is mandatory, if the damageTypeCode is YES.
    extent_description: Optional[list[FITranslatedText]] = []
    # This is mandatory, if the damageTypeCode is YES.
    repair_description: Optional[list[FITranslatedText]] = []
    # Unique identifier for the document attachment. A separate document can be
    # attached which describes the damages in more detail.
    attachments: Optional[list[DamageAttachment]] = []


class UpdateNameDamageAttachmentsObject(BaseSchema):
    name: str


class UpdateNameDamageAttachments(BaseSchema):
    update_infos: list[UpdateNameDamageAttachmentsObject]


class RemoveDamageAttachments(BaseSchema):
    ids: list[str]


class FILivingComfortFactors(BaseSchema):
    living_comfort_factor_code: FILivingComfortFactorCodeEnum
    description: Optional[str] = None


class FIKey(BaseSchema):
    key_usage_code: Optional[FIKeyUsageCodeEnum] = None
    key_type_code: Optional[FIKeyTypeCodeEnum] = None
    # Describes the key in more details
    description: Optional[str] = None
    key_count: Optional[int] = None


class FIKeyManagement(BaseSchema):
    keys_to_be_ordered_from_housing_manager: Optional[FIChoiceEnum] = None
    keys_handed_over_by_housing_company: Optional[FIChoiceEnum] = None
    number_of_keys_handed_over_by_housing_company: Optional[int] = None
    keycard_available: Optional[FIChoiceEnum] = None
    # Describes the keycard possession, for example when the keycard is handed over.
    description: Optional[str] = None
    keys: Optional[list[FIKey]] = None


class FILeaseDetails(BaseSchema):
    payment_date: Optional[str] = None
    # Provides additional information about the deposit
    deposit_description: Optional[str] = None
    # Specifies the rent deposit
    deposit_amount: Optional[int] = None
    currency_code: Optional[str] = None
    # Specifies the duration of the lease
    lease_type_code: Optional[FILeaseTypeCodeEnum] = None
    fixed_lease_start_date: Optional[str] = None
    fixed_lease_end_date: Optional[str] = None
    lease_start_date: Optional[str] = None


class _FIRealtyBasicDetails(BaseSchema):
    status: Status = Status.DRAFT
    # Describes the montly rent that's paid by the tenant. The value of this
    # attribute must be set when the categoryCode is SALE and the value of the
    # isRented attribute is YES OR the categoryCode is RENTAL.
    monthly_rent: Optional[int] = None
    # The selling price of the realty. The value of this attribute must be set when the categoryCode is SALE.
    selling_price: Optional[int] = None
    currency_code: str = DEFAULT_CURRENCY


class _FIRealtyFullDetails(_FIRealtyBasicDetails):
    new_building: Optional[FIChoiceEnum] = None
    # Specifies the agents which are associated with this realty.
    agents: Optional[list[FIAgent]] = []
    # Contains the consent provided by the principal (buyer or seller) to change details for this assignment.
    consent_to_change: Optional[FIConsentToChange] = None
    costs: Optional[list[FICost]] = []
    costs_description: Optional[list[FITranslatedText]] = None
    # Indicates whether the realty will be sold through auction.
    auction_allowed: Optional[bool] = None
    # The starting selling price amount if the realty is sold through auction.
    starting_price_amount: Optional[int] = None
    availability: Optional[FIRealtyAvailability] = None
    agency_office_id: Optional[str] = None
    is_rented: Optional[FIChoiceEnum] = None
    # address: Optional[FIAddress] = None
    # descriptions: Optional[list[FITranslatedText]] = []
    notify_if_price_changed: Optional[FIChoiceEnum] = None
    title: Optional[list[FITranslatedText]] = None
    transaction_description: Optional[list[FITranslatedText]] = []
    transaction_does_not_include: Optional[list[FITranslatedText]] = []
    transaction_includes: Optional[list[FITranslatedText]] = []
    condition: Optional[FIRealtyCondition] = None
    additional_area_measurement_information: Optional[list[FITranslatedText]] = []
    additional_information: Optional[FIRealtyAdditionalInformation] = None
    # Identifiers assigned for the realty in other systems.
    supplier_assigned_identifiers: Optional[list[FISupplierAssignedIdentifiers]] = []
    damages: Optional[list[FIDamages]] = []
    living_comfort_factors: Optional[list[FILivingComfortFactors]] = []
    key_management: Optional[FIKeyManagement] = None
    lease_details: Optional[FILeaseDetails] = None
    living_form_type_code: Optional[FILivingFormTypeCodeEnum] = None
    share: Optional[FIRealtyShare] = None


class FIRealtyCreateEdit(_FIRealtyFullDetails):
    fi_address: Optional[FIAddressCreate] = None


class FIRealtyListRead(_FIRealtyBasicDetails):
    id: int
    fi_address: FIAddressRead


class FIRealtyRead(_FIRealtyFullDetails, FIRealtyListRead):
    pass


class DamageAttachments(BaseSchema):
    id: int
    name: str
    url: str
