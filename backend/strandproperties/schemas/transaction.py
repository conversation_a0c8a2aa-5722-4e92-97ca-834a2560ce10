from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Union

from strandproperties.constants import OrderBy, SortEnum
from strandproperties.schemas.base import BaseSchema, PaginatedList
from strandproperties.schemas.param import PaginationParam
from strandproperties.schemas.user import UserBasicInfo


class ExpenseTransactionType(str, Enum):
    EXPENSE = "expense"
    DEPOSIT = "deposit"
    PAYOUT = "payout"
    GIFT = "gift"


class ExpenseTransactionStatus(str, Enum):
    SUCCEEDED = "succeeded"
    CANCELLED = "cancelled"
    FAILED = "failed"
    PROCESSING = "processing"
    REQUIRES_ACTION = "requires_action"


class ExpenseTransactionTargetBase(BaseSchema):
    target_type: str
    target_id: int


class ExpenseTransactionTargetRead(ExpenseTransactionTargetBase):
    id: int


class ExpenseTransactionTargetCreate(ExpenseTransactionTargetBase):
    pass


class ExpenseTransactionBase(BaseSchema):
    user_id: int
    amount: int
    type: ExpenseTransactionType
    description: Optional[str] = None
    meta: Optional[Dict] = None
    stripe_transaction_id: Optional[str] = None
    status: ExpenseTransactionStatus
    user: Optional[UserBasicInfo] = None


class ExpenseTransactionRead(ExpenseTransactionBase):
    id: int
    created_at: datetime
    updated_at: datetime
    targets: Optional[List[ExpenseTransactionTargetRead]] = None


class ExpenseTransactionListRead(BaseSchema):
    transactions: List[ExpenseTransactionRead]


class ExpenseTransactionCreate(ExpenseTransactionBase):
    targets: Optional[List[ExpenseTransactionTargetCreate]] = None


class ExpenseTransactionEdit(BaseSchema):
    user_id: Optional[int] = None
    amount: Optional[int] = None
    type: Optional[ExpenseTransactionType] = None
    description: Optional[str] = None
    meta: Optional[Dict] = None
    stripe_transaction_id: Optional[str] = None
    targets: Optional[List[ExpenseTransactionTargetCreate]] = None


class ExpenseTransactionSortColumn(str, Enum):
    DESCRIPTION = "description"
    AMOUNT = "amount"
    TYPE = "type"
    STATUS = "status"
    CREATED_AT = "created_at"
    UPDATED_AT = "updated_at"
    USER_ID = "user_id"


class ExpenseTransactionOrderBy(str, Enum):
    ALPHABETICAL_ASC = "alphabetical_asc"
    ALPHABETICAL_DESC = "alphabetical_desc"
    AMOUNT_ASC = "amount_asc"
    AMOUNT_DESC = "amount_desc"
    LATEST = "latest"
    OLDEST = "oldest"
    LATEST_UPDATED = "latest_updated"
    OLDEST_UPDATED = "oldest_updated"


class ExpenseTransactionFilterParam(PaginationParam):
    keyword: Optional[str] = None
    filter_by_me: Optional[bool] = None
    user_id: Optional[int] = None
    types: Optional[Union[List[ExpenseTransactionType], ExpenseTransactionType]] = None
    statuses: Optional[
        Union[List[ExpenseTransactionStatus], ExpenseTransactionStatus]
    ] = None
    order_by: ExpenseTransactionOrderBy = ExpenseTransactionOrderBy.LATEST
    sort_column: Optional[ExpenseTransactionSortColumn] = None
    sort_direction: Optional[SortEnum] = None


class ExpenseTransactionTargetEntity(str, Enum):
    ADVERTISEMENT = "advertisement"


TransactionList = PaginatedList[ExpenseTransactionRead]
