from datetime import date, datetime
from decimal import Decimal
from typing import List, Literal, Optional

from pydantic import <PERSON>asP<PERSON>, Field, field_serializer, model_validator
from typing_extensions import Self

from strandproperties.constants import (
    AttachmentDocumentType,
    DoSCommissionType,
    DoSDepositAccountType,
    Language,
    SowiseStatus,
)
from strandproperties.schemas.base import BaseSchema
from strandproperties.schemas.document import (
    DocumentAttachmentBase,
    DocumentBasicInfo,
    DocumentWithAttachments,
)
from strandproperties.schemas.office import OfficeRead
from strandproperties.schemas.property import PropertyBase, PropertyDetail
from strandproperties.schemas.user import UserBasicInfo


class DetailsOfSaleAgentBase(BaseSchema):
    commission_percentage: float | None = None
    commission_amount: int | None = None


class DetailsOfSaleAgent(DetailsOfSaleAgentBase):
    user: Optional[UserBasicInfo] = None


class DetailsOfSaleAgentCreate(DetailsOfSaleAgentBase):
    user_id: int


class DetailsOfSaleBasicData(BaseSchema):
    offer_agreed_date: date | None
    sale_price: float
    deposit_percentage: float | None = None
    deposit_amount: float | None = None
    deposit_account_type: Literal[
        DoSDepositAccountType.STRAND_CLIENTS_ACCOUNT,
        DoSDepositAccountType.STRAND_COMMISSION,
        DoSDepositAccountType.LAWYERS_ACCOUNT_BUYER,
        DoSDepositAccountType.LAWYERS_ACCOUNT_VENDOR,
        DoSDepositAccountType.DEVELOPERS_ACCOUNT,
        DoSDepositAccountType.VENDORS_ACCOUNT,
        DoSDepositAccountType.NO_DEPOSIT_PAID,
        DoSDepositAccountType.OTHER,
    ]
    deposit_paid_date: Optional[date] = None
    ppc_date: Optional[date] = None
    completion_notary_deadline: date | None
    notary_day_booked: date | None
    total_commission_amount: float
    total_commission_type: Literal[
        DoSCommissionType.PERCENT_PLUS_VAT,
        DoSCommissionType.AMOUNT_PLUS_VAT,
        DoSCommissionType.PERCENT_VAT_INCLUDED,
        DoSCommissionType.AMOUNT_VAT_INCLUDED,
    ]
    strand_commission_amount: float
    strand_commission_type: (
        Literal[
            DoSCommissionType.PERCENT_PLUS_VAT,
            DoSCommissionType.AMOUNT_PLUS_VAT,
            DoSCommissionType.PERCENT_VAT_INCLUDED,
            DoSCommissionType.AMOUNT_VAT_INCLUDED,
        ]
        | None
    )
    other_agency_name: str | None
    other_agency_commission_amount: float | None
    other_agency_commission_type: str | None
    notes: Optional[str] = None
    external_lead: str | None
    external_lead_percentage: float | None
    separate_invoice_for_each_seller: bool = True
    reviewer_office_id: int | None


class SellerDetailsOfSaleData(BaseSchema):

    seller_id: int
    invoice_percentage: float | None = None


class DetailsOfSaleAgentEdit(DetailsOfSaleAgentCreate):
    agent_order: int


class DetailsOfSaleCreateEdit(DetailsOfSaleBasicData):
    property_reference: str
    language: str | None = Language.ENGLISH.value
    buyers: list[int] | None = []
    sellers: list[SellerDetailsOfSaleData]
    send_for_review: bool = False
    save_as_draft: bool = False
    agents: list[DetailsOfSaleAgentEdit] | None = []
    offer_id: int | None = None
    status: SowiseStatus = None

    @model_validator(mode="after")
    def validate_invoice_percentage(self) -> Self:
        if not self.separate_invoice_for_each_seller:
            return self
        for seller in self.sellers:
            if not seller.invoice_percentage:
                raise ValueError(
                    f"Missing invoice_percentage for seller_id: {seller.seller_id} when separate invoice for each seller is True."
                )
        return self


class DetailsOfSaleStatus(BaseSchema):
    status: Literal[
        SowiseStatus.APPROVED,
        SowiseStatus.REQUESTING_CHANGES,
        SowiseStatus.NOTARIZED,
    ]
    notary_date: date | None = None
    comment: str | None = None


class DetailsOfSaleRequestChanges(BaseSchema):
    message: str


class DetailsOfSaleRead(DetailsOfSaleBasicData):
    id: int
    custom_reference_property: str | None
    document_id: str = Field(
        validation_alias=AliasPath("document", "sowise_id"),
    )
    agents: list[DetailsOfSaleAgent] = Field(
        validation_alias=AliasPath("realtors"),
    )
    status: str = Field(
        validation_alias=AliasPath("document", "status"),
    )


class DetailsOfSaleRequestChangesRead(BaseSchema):
    id: int
    message: str


class DetailsOfSaleListRead(DetailsOfSaleBasicData):
    id: int
    updated_at: datetime | None
    custom_reference_property: str | None
    document: DocumentBasicInfo = Field("document")
    created_user: UserBasicInfo
    review_office: OfficeRead | None
    property: Optional[PropertyBase] = None
    agents: list[DetailsOfSaleAgent] = Field(
        validation_alias=AliasPath("realtors"),
    )


class SellerDetailsOfSaleWithContact(SellerDetailsOfSaleData):
    id: int
    name: str = Field(
        validation_alias=AliasPath("seller", "name"),
    )
    proforma: Optional[DocumentBasicInfo] = Field(
        None,
        validation_alias=AliasPath("details_of_sale_invoice", "proforma"),
    )
    invoice_number: Optional[str] = Field(
        None,
        validation_alias=AliasPath("details_of_sale_invoice", "invoice_number"),
    )
    invoice: Optional[DocumentBasicInfo] = Field(
        None,
        validation_alias=AliasPath("details_of_sale_invoice", "document"),
    )


class BuyerDetailsOfSaleWithContact(BaseSchema):
    id: int
    name: str = Field(
        validation_alias=AliasPath("buyer", "name"),
    )


class DetailsOfSaleAgentDetail(DetailsOfSaleAgent):
    document: Optional[DocumentBasicInfo] = None
    document_attachments: List[DocumentAttachmentBase] = None


class DetailsOfSaleDetailRead(DetailsOfSaleRead):
    document: DocumentWithAttachments = Field("document")
    sellers: List[SellerDetailsOfSaleWithContact] = []
    buyers: List[BuyerDetailsOfSaleWithContact] = []
    agent_invoices: List[DocumentBasicInfo] = []
    property: Optional[PropertyDetail] = None
    agents: list[DetailsOfSaleAgentDetail] = Field(
        validation_alias=AliasPath("realtors"),
    )
    offer_created_by: Optional[int] = None
    created_user: UserBasicInfo

    @field_serializer("agent_invoices")
    def serializer_agent_invoices(self, agent_invoices: list, _info):
        "get agent invoices from document"
        return [
            attachment
            for attachment in self.document.document_attachments
            if attachment.attachment_type == AttachmentDocumentType.AGENT_INVOICES
        ]


class DocumentAttachment(BaseSchema):
    document: bytes


class RemoveDetailsOfSaleAttachments(BaseSchema):
    sowise_ids: list[str]


class UpdateNameDetailsOfSaleAttachmentsObject(BaseSchema):
    sowise_id: str
    name: str


class UpdateNameDetailsOfSaleAttachments(BaseSchema):
    update_infos: list[UpdateNameDetailsOfSaleAttachmentsObject]


class UploadDetailsOfSaleAttachments(BaseSchema):
    attachments: list[DocumentAttachment]


class UpdateSellerDetailsOfSale(BaseSchema):
    id: int
    invoice_number: str


class ApproveDetailsOfSale(BaseSchema):
    invoice_date: datetime
    invoice_due_date: datetime
    issued_by: int
    agent_id: int
    sellers: Optional[List[UpdateSellerDetailsOfSale]] = []
    language: str | None = Language.ENGLISH.value


class TransactionListRead(BaseSchema):
    id: int
    transaction_reference: Optional[str] = None
    property_description: Optional[str] = None
    sale_price: Optional[float] = None
    total_commission: Optional[float] = None
    total_commission_percent: Optional[float] = None
    strand_commission_earned: Optional[float] = None
    agent_commission_earned: Optional[float] = None


class ReportKpiRead(BaseSchema):
    total_number_of_transactions: Optional[int] = None
    total_sale_price: Optional[float] = None
    total_commission: Optional[float] = None
    total_commission_percent: Optional[float] = None
    total_strand_commission_earned: Optional[float] = None
    strand_commission_percent: Optional[float] = None
    strand_commission_of_full_commission_percent: Optional[float] = None
    total_agent_commission_earned: Optional[float] = None


class ReportKpiReadCompare(BaseSchema):
    current: Optional[ReportKpiRead | dict] = None
    previous_month: Optional[ReportKpiRead | dict] = None
    last_year: Optional[ReportKpiRead | dict] = None
