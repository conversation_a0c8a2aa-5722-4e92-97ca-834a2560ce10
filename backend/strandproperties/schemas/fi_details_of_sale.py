from datetime import date, datetime
from typing import List, Optional
from strandproperties.schemas.fi_property.fi_property import FIPropertyRead

from strandproperties.schemas.user import UserRead

from strandproperties.schemas.base import BaseSchema
from enum import Enum
from strandproperties.views.exceptions import CustomValidationError


class FIDetailsOfSaleStatus(str, Enum):
    DRAFT = "draft"
    VALIDATED = "validated"
    LOCKED = "locked"


class FIDetailsOfSaleOwnershipType(str, Enum):
    PERCENTAGE = "percentage"
    FRACTION = "fraction"


class FIDetailsOfSaleTransactionMethod(str, Enum):
    TRADITIONAL = "traditional"
    DIAS = "dias"


class FIDetailsOfSaleRecipientRole(str, Enum):
    SELLING = "selling"
    BROKERAGE_FIRM = "brokerage_firm"


class FIDetailsOfSaleRecipientType(str, Enum):
    CONTACT = "contact"
    USER = "user"


class _FIDetailsOfSaleBase(BaseSchema):
    property_id: int
    status: Optional[FIDetailsOfSaleStatus] = None
    ownership_type: Optional[FIDetailsOfSaleOwnershipType] = None
    transaction_method: Optional[FIDetailsOfSaleTransactionMethod] = None
    assignment_started_at: Optional[date] = None
    estimated_transaction_date: Optional[date] = None
    sale_duration_days: Optional[int] = None
    offer_count: Optional[int] = None
    highest_rejected_offer: Optional[float] = None
    sale_price: Optional[float] = None
    debt_free_price: Optional[float] = None
    mortgage_bank: Optional[str] = None
    notes: Optional[str] = None
    commission_amount_total: Optional[float] = None
    commission_percent: Optional[float] = None
    commission_vat_percent: Optional[float] = None
    commission_vat_included: Optional[bool] = None
    commission_amount_without_vat: Optional[float] = None
    commission_vat_amount: Optional[float] = None
    commission_amount_with_vat: Optional[float] = None
    sellers: Optional[List["FISellerDetailsOfSaleSchema"]] = None
    buyers: Optional[List["FIBuyerDetailsOfSaleSchema"]] = None
    recipients: Optional[List["FIRecipientDetailsOfSaleSchema"]] = None


class FIDetailsOfSaleCreate(_FIDetailsOfSaleBase):
    pass


class FIDetailsOfSaleEdit(_FIDetailsOfSaleBase):
    pass


class FIDetailsOfSaleStatusUpdate(BaseSchema):
    status: FIDetailsOfSaleStatus


class FIDetailsOfSaleRead(_FIDetailsOfSaleBase):
    id: int
    created_at: datetime
    property: FIPropertyRead


class FISellerDetailsOfSaleSchema(BaseSchema):
    seller_id: int
    ownership_share_percent: Optional[float] = None


class FIBuyerDetailsOfSaleSchema(BaseSchema):
    buyer_id: int
    ownership_share_percent: Optional[float] = None


class FIRecipientDetailsOfSaleSchema(BaseSchema):
    user_id: Optional[int] = None
    role: FIDetailsOfSaleRecipientRole
    commission_percent: Optional[float] = None
    commission_amount: Optional[float] = None
    type: FIDetailsOfSaleRecipientType
    user: Optional[UserRead] = None


def validate_complete_schema(data: FIDetailsOfSaleCreate):

    errors = {}
    required_fields = {
        "property_id": "Property ID",
        "ownership_type": "Ownership Type",
        "transaction_method": "Transaction Method",
        "assignment_started_at": "Assignment Started At",
        "estimated_transaction_date": "Estimated Transaction Date",
        "sale_duration_days": "Sale Duration Days",
        "offer_count": "Offer Count",
        "highest_rejected_offer": "Highest Rejected Offer",
        "sale_price": "Sale Price",
        "debt_free_price": "Debt Free Price",
        "commission_amount_total": "Commission Amount Total",
        "commission_percent": "Commission Percent",
        "commission_vat_percent": "Commission VAT Percent",
        "commission_vat_included": "Commission VAT Included",
        "commission_amount_without_vat": "Commission Amount Without VAT",
        "commission_vat_amount": "Commission VAT Amount",
        "commission_amount_with_vat": "Commission Amount With VAT",
        "sellers": "Sellers",
        "buyers": "Buyers",
        "recipients": "Recipients",
    }

    for field, field_name in required_fields.items():
        if getattr(data, field) is None:
            errors[field] = {"message": f"{field_name} is required.", "code": "missing"}

    # Validate ownership percentages
    total_seller_ownership = sum(
        seller.ownership_share_percent for seller in data.sellers or []
    )
    total_buyer_ownership = sum(
        buyer.ownership_share_percent for buyer in data.buyers or []
    )

    if total_seller_ownership != 100:
        errors["sellers.ownership_share_percent"] = {
            "message": "The combined ownership share percent for sellers must be 100.",
            "code": "invalid",
        }

    if total_buyer_ownership != 100:
        errors["buyers.ownership_share_percent"] = {
            "message": "The combined ownership share percent for buyers must be 100.",
            "code": "invalid",
        }

    # Validate commission percentages
    total_recipient_commission = sum(
        recipient.commission_percent for recipient in data.recipients or []
    )
    if total_recipient_commission != 100:
        errors["commission_percent"] = {
            "message": "The combined commission percent for recipients must be 100.",
            "code": "invalid",
        }

    if errors:
        raise CustomValidationError(errors)
    return data
