from datetime import date
from typing import List, Optional

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    Date,
    Float,
    <PERSON><PERSON><PERSON>,
    Integer,
    String,
    UniqueConstraint,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import (
    FISalesAgreementAcquisitionEnum,
    FISalesAgreementAvailabilityEnum,
    FISalesAgreementCommissionBasisEnum,
    FISalesAgreementCommissionTypeEnum,
    FISalesAgreementLeaseAgreementEnum,
    FISalesAgreementPaymentTermsEnum,
    FISalesAgreementShareRegisterFormatEnum,
    FISalesAgreementStatusEnum,
    FISalesAgreementTaxConsequenceEnum,
    FISalesAgreementTermEnum,
)
from strandproperties.models.base import BaseModel
from strandproperties.models.document_signing import DocumentSigning
from strandproperties.models.user import User


class FISalesAgreement(BaseModel):
    __tablename__ = "fi_sales_agreement"

    property_id = Column(
        ForeignKey("fi_property.id", ondelete="CASCADE"),
        nullable=False,
    )

    status: Mapped[Optional[FISalesAgreementStatusEnum]] = mapped_column(
        String(50), nullable=False, default=FISalesAgreementStatusEnum.DRAFT
    )

    property = relationship("FIProperty", foreign_keys=[property_id], viewonly=True)

    contacts = relationship("Contact", secondary="fi_sales_agreement_contact")

    consenters = relationship("Contact", secondary="fi_sales_agreement_consenter")

    realtor_users: Mapped[List["User"]] = relationship(
        "User",
        secondary="fi_sales_agreement_realtor",
        order_by="FISalesAgreementRealtor.sorting_index",
    )

    signings: Mapped[List["DocumentSigning"]] = relationship(
        "DocumentSigning",
        secondary="document_signing_entity",
        primaryjoin=(
            "and_("
            "FISalesAgreement.id == foreign(DocumentSigningEntity.entity_id), "
            "DocumentSigningEntity.entity_type == 'fi_sales_agreement'"
            ")"
        ),
        secondaryjoin="foreign(DocumentSigningEntity.document_signing_id) == DocumentSigning.id",
        viewonly=True,
    )

    contact_acquisitions: Mapped[List["FISalesAgreementContactAcquisition"]] = (
        relationship(
            "FISalesAgreementContactAcquisition",
            back_populates="fi_sales_agreement",
            cascade="all, delete-orphan",
        )
    )

    agreed_marketing_methods: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )
    separate_marketing_appendix: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )

    unencumbered_price_request: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    average_selling_time_estimate: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )
    unencumbered_price_request_estimate: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    shares_include_loan: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    loan_amount: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    loan_details: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    price_including_loan: Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    payment_term_other: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )
    factors_affecting_sales: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )

    date_when_available: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    availability_details: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )
    tenant_name: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    tenant_contact_details: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )
    lease_amount: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    lease_deposit: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    lease_start_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    lease_end_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)

    lease_terminated: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    tenant_has_paid_rent_on_time: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    tenant_paying_rent_details: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )
    lease_agreement_details: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )

    restrictive_right_of_user: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    restrictive_right_of_user_details: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )

    written_consent_to_transfer: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )

    belongs_to_business_activities: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    assignment_validity: Mapped[Optional[FISalesAgreementTermEnum]] = mapped_column(
        String(50), nullable=True
    )
    assignment_validity_renewal_period: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True
    )
    start_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    end_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)

    commission_fixed: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    commission_other: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    commission_percentage: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    commission_details: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )

    other_expenses_details: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )
    expense_if_no_commission: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )

    unpaid_maintenance_charge: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    unpaid_maintenance_charge_amount: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )

    digital_trading_allowed: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    is_domestic_sale: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    start_assignment_immediately: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    start_marketing_after_cancel_period: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    customer_asked_to_read_privacy_policy: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    previous_external_sales_agreement: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    previous_external_sales_agreement_details: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )
    additional_details: Mapped[Optional[str]] = mapped_column(
        String(500), nullable=True
    )
    created_by = Column(ForeignKey("user.id"), nullable=True)
    created_user = relationship("User", foreign_keys=[created_by])

    payment_terms: Mapped[Optional[FISalesAgreementPaymentTermsEnum]] = mapped_column(
        String(50)
    )

    availability: Mapped[Optional[FISalesAgreementAvailabilityEnum]] = mapped_column(
        String(50)
    )
    lease_agreement: Mapped[Optional[FISalesAgreementLeaseAgreementEnum]] = (
        mapped_column(String(50))
    )

    lease_agreement_term: Mapped[Optional[FISalesAgreementTermEnum]] = mapped_column(
        String(50), nullable=True
    )
    commission_basis_code: Mapped[Optional[FISalesAgreementCommissionBasisEnum]] = (
        mapped_column(String(50))
    )
    commission_type: Mapped[Optional[FISalesAgreementCommissionTypeEnum]] = (
        mapped_column(String(50))
    )
    vat: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)


class FISalesAgreementContactAcquisition(BaseModel):
    __tablename__ = "fi_sales_agreement_contact_acquisition"
    __table_args__ = (
        UniqueConstraint(
            "fi_sales_agreement_id",
            "contact_id",
            name="uq_fi_sales_agreement_contact_acquisition",
        ),
    )

    contact_id = Column(ForeignKey("contact.id"), nullable=False)
    fi_sales_agreement_id = Column(
        ForeignKey("fi_sales_agreement.id", ondelete="CASCADE"),
        nullable=False,
    )

    fi_sales_agreement = relationship(
        "FISalesAgreement",
        back_populates="contact_acquisitions",
        lazy="noload",
    )

    acquisition: Mapped[Optional[FISalesAgreementAcquisitionEnum]] = mapped_column(
        String(50)
    )

    seller_is_married_or_in_registered_relationship: Mapped[Optional[bool]] = (
        mapped_column(Boolean)
    )
    seller_has_been_married_or_in_registered_relationship: Mapped[Optional[bool]] = (
        mapped_column(Boolean)
    )
    seller_has_spouses_consent: Mapped[Optional[bool]] = mapped_column(Boolean)

    legal_partitioning_is_complete: Mapped[Optional[bool]] = mapped_column(Boolean)
    divorce_legally_binding: Mapped[Optional[bool]] = mapped_column(Boolean)

    acquisition_date: Mapped[Optional[date]] = mapped_column(Date)
    acquisition_cost: Mapped[Optional[str]] = mapped_column(String(250))

    client_has_used_residence_as_residence: Mapped[Optional[bool]] = mapped_column(
        Boolean
    )
    residency_start_date: Mapped[Optional[date]] = mapped_column(Date)
    residency_end_date: Mapped[Optional[date]] = mapped_column(Date)

    share_register_format: Mapped[Optional[FISalesAgreementShareRegisterFormatEnum]] = (
        mapped_column(String(50))
    )

    share_register_storage: Mapped[Optional[str]] = mapped_column(String(250))

    tax_consequence: Mapped[Optional[FISalesAgreementTaxConsequenceEnum]] = (
        mapped_column(String(50))
    )


class FISalesAgreementRealtor(BaseModel):
    __tablename__ = "fi_sales_agreement_realtor"
    __table_args__ = (
        UniqueConstraint(
            "fi_sales_agreement_id", "user_id", name="uq_fi_sales_agreement_realtor"
        ),
    )

    sorting_index = Column(Integer, nullable=False)

    user_id = Column(ForeignKey("user.id"), nullable=False)

    fi_sales_agreement_id = Column(
        ForeignKey("fi_sales_agreement.id"),
        nullable=False,
    )


class FISalesAgreementContact(BaseModel):
    __tablename__ = "fi_sales_agreement_contact"
    __table_args__ = (
        UniqueConstraint(
            "fi_sales_agreement_id", "contact_id", name="uq_fi_sales_agreement_contact"
        ),
    )

    contact_id = Column(ForeignKey("contact.id"), nullable=False)

    fi_sales_agreement_id = Column(
        ForeignKey("fi_sales_agreement.id"),
        nullable=False,
    )


class FISalesAgreementConsenter(BaseModel):
    __tablename__ = "fi_sales_agreement_consenter"
    __table_args__ = (
        UniqueConstraint(
            "fi_sales_agreement_id",
            "contact_id",
            name="uq_fi_sales_agreement_consenter",
        ),
    )

    contact_id = Column(ForeignKey("contact.id"), nullable=False)

    fi_sales_agreement_id = Column(
        ForeignKey("fi_sales_agreement.id"),
        nullable=False,
    )
