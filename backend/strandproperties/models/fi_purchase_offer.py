from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, Date, DateTime, Float, ForeignKey, String
from sqlalchemy.ext.mutable import MutableList
from sqlalchemy.orm import Mapped, mapped_column, relationship

from strandproperties.constants import FIPurchaseOfferStatusEnum
from strandproperties.models.base import BaseModel
from strandproperties.models.document_signing import DocumentSigning
from strandproperties.schemas.fi_purchase_offer import (
    FIPurchaseOfferReviewedDocument,
    FIPurchaseOfferTermOfSale,
)


class FIPurchaseOffer(BaseModel):
    __tablename__ = "fi_purchase_offer"

    property_id = Column(
        ForeignKey("fi_property.id", ondelete="CASCADE"),
        nullable=False,
    )

    status: Mapped[Optional[FIPurchaseOfferStatusEnum]] = mapped_column(
        String(50), nullable=True, default=FIPurchaseOfferStatusEnum.DRAFT
    )

    created_by = Column(ForeignKey("user.id"), nullable=True)

    property = relationship("FIProperty", foreign_keys=[property_id], viewonly=True)

    counter_offers = relationship(
        "FICounterOffer",
        back_populates="purchase_offer",
        cascade="all, delete-orphan",
        lazy="select",
    )

    buyers = relationship(
        "Contact",
        secondary="fi_purchase_offer_buyer",
    )

    sellers = relationship(
        "Contact",
        secondary="fi_purchase_offer_seller",
    )

    signings: Mapped[List["DocumentSigning"]] = relationship(
        "DocumentSigning",
        secondary="document_signing_entity",
        primaryjoin=(
            "and_("
            "FIPurchaseOffer.id == foreign(DocumentSigningEntity.entity_id), "
            "DocumentSigningEntity.entity_type == 'fi_purchase_offer'"
            ")"
        ),
        secondaryjoin="foreign(DocumentSigningEntity.document_signing_id) == DocumentSigning.id",
        viewonly=True,
    )

    reviewed_documents: Mapped[List[FIPurchaseOfferReviewedDocument]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True, default=list
    )
    terms_of_sale: Mapped[List[FIPurchaseOfferTermOfSale]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True, default=list
    )

    unencumbered_price: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    loan_amount: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    price_including_loan: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    loan_date: Mapped[Optional[Date]] = mapped_column(Date, nullable=True)
    building_manager_certificate_date: Mapped[Optional[Date]] = mapped_column(
        Date, nullable=True
    )
    payment_method: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    payment_schedule_and_terms: Mapped[Optional[str]] = mapped_column(
        String(500), nullable=True
    )
    transfer_right_of_use: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True
    )
    transfer_right_of_use_latest: Mapped[Optional[DateTime]] = mapped_column(
        DateTime, nullable=True
    )
    availability_delay_fee: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    delay_fee_per_started_week: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    is_rented: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    buyer_receives_rent_starting: Mapped[Optional[Date]] = mapped_column(
        Date, nullable=True
    )
    buyer_receives_rental_deposit_latest: Mapped[Optional[Date]] = mapped_column(
        Date, nullable=True
    )
    buyer_responsible_for_costs_starting: Mapped[Optional[Date]] = mapped_column(
        Date, nullable=True
    )
    buyer_responsible_for_capital_expenditure_charge_starting: Mapped[
        Optional[Date]
    ] = mapped_column(Date, nullable=True)
    digital_purchase: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    digital_purchase_expenses: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True
    )
    digital_purchase_expenses_details: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )
    standard_compensation: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    down_payment: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    down_payment_term: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    valid_until: Mapped[Optional[DateTime]] = mapped_column(DateTime, nullable=True)
    signed_latest: Mapped[Optional[Date]] = mapped_column(Date, nullable=True)
    accept_offer_email: Mapped[Optional[str]] = mapped_column(
        String(250), nullable=True
    )
    accept_offer_phone: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    has_read_privacy_policy: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )


class FIPurchaseOfferBuyer(BaseModel):
    __tablename__ = "fi_purchase_offer_buyer"

    purchase_offer_id: Mapped[int] = mapped_column(
        ForeignKey("fi_purchase_offer.id", ondelete="CASCADE"), nullable=False
    )
    buyer_id: Mapped[int] = mapped_column(
        ForeignKey("contact.id", ondelete="CASCADE"), nullable=False
    )


class FIPurchaseOfferSeller(BaseModel):
    __tablename__ = "fi_purchase_offer_seller"

    purchase_offer_id: Mapped[int] = mapped_column(
        ForeignKey("fi_purchase_offer.id", ondelete="CASCADE"), nullable=False
    )
    seller_id: Mapped[int] = mapped_column(
        ForeignKey("contact.id", ondelete="CASCADE"), nullable=False
    )
