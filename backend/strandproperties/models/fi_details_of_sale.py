from datetime import date
from typing import Optional
from sqlalchemy import (
    <PERSON>umn,
    Enum as S<PERSON><PERSON><PERSON>,
    Float,
    Integer,
    String,
    Text,
    Boolean,
    ForeignKey,
    Date,
    UniqueConstraint,
    Float,
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from strandproperties.models.base import BaseModel
from strandproperties.schemas.fi_details_of_sale import (
    FIDetailsOfSaleOwnershipType,
    FIDetailsOfSaleTransactionMethod,
    FIDetailsOfSaleRecipientRole,
    FIDetailsOfSaleRecipientType,
    FIDetailsOfSaleStatus,
)


class FIDetailsOfSale(BaseModel):
    __tablename__ = "fi_details_of_sale"

    property_id = Column(
        ForeignKey("fi_property.id", ondelete="CASCADE"),
        nullable=False,
    )
    status: Mapped[FIDetailsOfSaleStatus] = mapped_column(
        SQLEnum(FIDetailsOfSaleStatus),
        nullable=False,
        default=FIDetailsOfSaleStatus.DRAFT,
    )

    property = relationship("FIProperty", foreign_keys=[property_id], viewonly=True)

    ownership_type: Mapped[Optional[FIDetailsOfSaleOwnershipType]] = mapped_column(
        SQLEnum(FIDetailsOfSaleOwnershipType), nullable=True
    )
    transaction_method: Mapped[Optional[FIDetailsOfSaleTransactionMethod]] = (
        mapped_column(SQLEnum(FIDetailsOfSaleTransactionMethod), nullable=True)
    )
    assignment_started_at: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    estimated_transaction_date: Mapped[Optional[date]] = mapped_column(
        Date, nullable=True
    )
    sale_duration_days: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    offer_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    highest_rejected_offer: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    sale_price: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    debt_free_price: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    mortgage_bank: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    commission_amount_total: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    commission_percent: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    commission_vat_percent: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    commission_vat_included: Mapped[Optional[bool]] = mapped_column(
        Boolean, nullable=True
    )
    commission_amount_without_vat: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    commission_vat_amount: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    commission_amount_with_vat: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )

    sellers = relationship("FISellerDetailsOfSale", back_populates="details_of_sale")
    buyers = relationship("FIBuyerDetailsOfSale", back_populates="details_of_sale")
    recipients = relationship(
        "FIRecipientDetailsOfSale", back_populates="details_of_sale"
    )


class FISellerDetailsOfSale(BaseModel):
    __tablename__ = "fi_seller_details_of_sale"
    __table_args__ = (UniqueConstraint("fi_details_of_sale_id", "seller_id"),)

    fi_details_of_sale_id: Mapped[int] = Column(
        ForeignKey(FIDetailsOfSale.id), index=True
    )
    ownership_share_percent: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    details_of_sale = relationship("FIDetailsOfSale", back_populates="sellers")

    seller_id: Mapped[int] = Column(ForeignKey("contact.id"), index=True)
    seller = relationship("Contact", foreign_keys=[seller_id], viewonly=True)


class FIBuyerDetailsOfSale(BaseModel):
    __tablename__ = "fi_buyer_details_of_sale"
    __table_args__ = (UniqueConstraint("fi_details_of_sale_id", "buyer_id"),)

    fi_details_of_sale_id: Mapped[int] = Column(
        ForeignKey(FIDetailsOfSale.id), index=True
    )
    buyer_id: Mapped[int] = Column(ForeignKey("contact.id"), index=True)
    ownership_share_percent: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True
    )
    details_of_sale = relationship("FIDetailsOfSale", back_populates="buyers")


class FIRecipientDetailsOfSale(BaseModel):
    __tablename__ = "fi_recipient_details_of_sale"
    # TODO: might need to support also contacts here aswell
    user_id: Mapped[int] = Column(ForeignKey("user.id"), index=True)
    user = relationship("User", foreign_keys=[user_id], viewonly=True)

    fi_details_of_sale_id: Mapped[int] = Column(
        ForeignKey(FIDetailsOfSale.id), index=True
    )
    role: Mapped[FIDetailsOfSaleRecipientRole] = mapped_column(
        SQLEnum(FIDetailsOfSaleRecipientRole), nullable=False
    )
    commission_percent: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    commission_amount: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    type: Mapped[FIDetailsOfSaleRecipientType] = mapped_column(
        SQLEnum(FIDetailsOfSaleRecipientType), nullable=False
    )
    details_of_sale = relationship("FIDetailsOfSale", back_populates="recipients")
