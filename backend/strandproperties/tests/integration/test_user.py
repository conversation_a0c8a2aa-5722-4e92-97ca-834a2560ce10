from sqlalchemy import select

from strandproperties.config import app_cfg
from strandproperties.constants import (
    CompanyType,
    EventLogAction,
    EventLogObjectType,
    RoleType,
)
from strandproperties.models.event_log import EventLog
from strandproperties.tests.conftest import db_session


def test_me_admin(route_url, api, admin1, organization1):
    r = api("get", route_url("user.me"), status=200)
    assert r["email"] == admin1.email
    assert len(r["roles"]) == 1
    assert r["roles"][0]["role"] == RoleType.ADMIN
    assert r["roles"][0]["organization"]["id"] == organization1.id
    assert r["roles"][0]["organization"]["countryCode"] == organization1.country_code
    assert r["roles"][0]["organization"]["currency"] == organization1.currency
    assert r["roles"][0]["organization"]["language"] == organization1.language


def test_me_realtor(route_url, api, realtor1, organization1):
    r = api("get", route_url("user.me"), role=realtor1._roles[0], status=200)
    assert r["email"] == realtor1.email
    assert len(r["roles"]) == 1
    assert r["roles"][0]["role"] == RoleType.REALTOR
    assert r["roles"][0]["organization"]["id"] == organization1.id
    assert r["roles"][0]["organization"]["countryCode"] == organization1.country_code
    assert r["roles"][0]["organization"]["currency"] == organization1.currency
    assert r["roles"][0]["organization"]["language"] == organization1.language


def test_me_photographer(route_url, api, photographer1, organization1):
    r = api("get", route_url("user.me"), role=photographer1._roles[0], status=200)
    assert r["email"] == photographer1.email
    assert len(r["roles"]) == 1
    assert r["roles"][0]["role"] == RoleType.PHOTOGRAPHER
    assert r["roles"][0]["organization"]["id"] == organization1.id
    assert r["roles"][0]["organization"]["countryCode"] == organization1.country_code
    assert r["roles"][0]["organization"]["currency"] == organization1.currency
    assert r["roles"][0]["organization"]["language"] == organization1.language


def test_create_user_with_multiple_roles(
    route_url,
    api,
    db_session,
    admin1,
    organization1,
    organization2,
    mock_send_set_password_email,
):
    # This test will mock the send_set_password_email function to do nothing

    r = api(
        "post",
        route_url("user.list_create"),
        {
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "phone_number": "************",
            "roles": [
                {"organization_id": organization1.id, "role": "Realtor"},
                {"organization_id": organization2.id, "role": "Photographer"},
            ],
        },
        status=201,  # Expecting a 201 Created since the email sending does not cause an error
        role=admin1._roles[0],
    )

    # Verify the user has been created with the correct roles
    r = api("get", route_url("user.read_edit", id=r["id"]), status=200)

    event_log = db_session.scalars(
        select(EventLog).where(
            EventLog.object_type == EventLogObjectType.USER,
            EventLog.object_id == r["id"],
            EventLog.action == EventLogAction.CREATED,
        )
    ).one()

    assert event_log.details == {
        "user": {
            "after": {
                "id": r["id"],
                "name": "John Doe",
            }
        }
    }
    assert event_log.data_after == {
        "id": r["id"],
    }

    assert len(r["roles"]) == 2
    assert any(
        role["role"] == "Realtor" and role["organization"]["id"] == organization1.id
        for role in r["roles"]
    )
    assert any(
        role["role"] == "Photographer"
        and role["organization"]["id"] == organization2.id
        for role in r["roles"]
    )


def test_create_user_with_invalid_role(
    route_url, api, admin1, organization1, mock_send_set_password_email
):
    # This test will mock the send_set_password_email function to do nothing

    r = api(
        "post",
        route_url("user.list_create"),
        {
            "first_name": "Invalid",
            "last_name": "Role",
            "email": "<EMAIL>",
            "phone_number": "************",
            "roles": [
                {"organization_id": organization1.id, "role": "InvalidRole"},
            ],
        },
        status=400,  # Expecting a 400 Bad Request due to invalid role
        role=admin1._roles[0],
    )

    # Verify that the response contains the expected error message
    assert r.json["error"] == "Invalid user information"


def test_create_user_with_single_role(
    route_url, api, admin1, organization1, mock_send_set_password_email
):
    # Admin creates a new user with a role in one organization
    r = api(
        "post",
        route_url("user.list_create"),
        {
            "first_name": "Jane",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "phone_number": "************",
            "roles": [
                {"organizationId": organization1.id, "role": "Realtor"},
            ],
        },
        status=201,
        role=admin1._roles[0],
    )

    # Verify the user has been created with the correct role
    r = api("get", route_url("user.read_edit", id=r["id"]), status=200)

    assert len(r["roles"]) == 1
    assert r["roles"][0]["role"] == "Realtor"
    assert r["roles"][0]["organization"]["id"] == organization1.id


def test_create_user_with_invalid_role(
    route_url, api, admin1, organization1, mock_send_set_password_email
):
    # Admin tries to create a new user with an invalid role
    r = api(
        "post",
        route_url("user.list_create"),
        {
            "first_name": "Invalid",
            "last_name": "Role",
            "email": "<EMAIL>",
            "phone_number": "************",
            "roles": [
                {"organizationId": organization1.id, "role": "InvalidRole"},
            ],
        },
        status=400,  # Expecting a 400 Bad Request due to invalid role
        role=admin1._roles[0],
    )

    # Check the response for an error
    assert "error" in r
    assert r["error"] == "Invalid user information"


def test_create_user_with_invalid_organization_id(route_url, api, admin1):
    # Admin tries to create a new user with an invalid organization ID
    r = api(
        "post",
        route_url("user.list_create"),
        {
            "first_name": "Invalid",
            "last_name": "Organization",
            "email": "<EMAIL>",
            "phone_number": "************",
            "roles": [
                {
                    "organizationId": 9999,
                    "role": "Realtor",
                },  # Assuming 9999 is an invalid ID
            ],
        },
        status=500,  # Expecting a 500 due to invalid organization ID
        role=admin1._roles[0],
    )

    # Check the response for an error
    assert "error" in r
    assert r["error"] == "Unexpected error occurred"


def test_create_user_missing_required_fields(route_url, api, admin1):
    # Admin tries to create a new user with missing required fields
    r = api(
        "post",
        route_url("user.list_create"),
        {
            "first_name": "John",
            # Missing last_name and email
            "phone_number": "************",
            "roles": [
                {"organizationId": 1, "role": "Realtor"},
            ],
        },
        status=400,  # Expecting a 400 Bad Request due to missing required fields
        role=admin1._roles[0],
    )

    # Check the response for an error
    assert "error" in r
    assert r["error"] == "Invalid user information"


def test_create_user_without_admin_permission(route_url, api, realtor1, organization1):
    # A non-admin user tries to create a new user
    r = api(
        "post",
        route_url("user.list_create"),
        {
            "first_name": "Not",
            "last_name": "Admin",
            "email": "<EMAIL>",
            "phone_number": "************",
            "roles": [
                {"organizationId": organization1.id, "role": "Realtor"},
            ],
        },
        status=403,  # Expecting a 403 Forbidden due to lack of admin permission
        role=realtor1._roles[0],
    )

    # Check the response for an error
    assert "error" in r
    assert r["error"] == "Only admin users can create users."


def test_read_user(route_url, api, realtor1, organization1):
    r = api("get", route_url("user.read_edit", id=realtor1.id), status=200)
    assert r["id"] == realtor1.id
    assert r["firstName"] == realtor1.first_name
    assert r["roaiib"] == realtor1.roaiib
    assert r["liabilityInsurance"] == realtor1.liability_insurance
    assert r["roles"][0]["organization"]["currency"] == organization1.currency
    assert r["roles"][0]["organization"]["language"] == organization1.language


def test_read_non_existent_user(route_url, api):
    api("get", route_url("user.read_edit", id="1231"), status=404)


def test_list_users(route_url, api, realtor1, unactive_realtor):
    r = api("get", route_url("user.list_create"), status=200)
    assert len(r["records"]) == 3

    r = api(
        "get", route_url("user.list_create"), params={"onlyActive": True}, status=200
    )
    assert len(r["records"]) == 2

    r = api(
        "get", route_url("user.list_create"), params={"onlyActive": False}, status=200
    )
    assert len(r["records"]) == 1


def test_list_users_when_photographer(
    route_url, api, realtor1, unactive_realtor, photographer1
):
    r = api(
        "get", route_url("user.list_create"), status=200, role=photographer1._roles[0]
    )
    assert len(r["records"]) == 3  # admin1, realtor1, photographer1 (only active users)


def test_list_users_in_an_organization(route_url, api, organization2):
    r = api(
        "get",
        route_url(
            "user.list_create",
            params={"onlyActie": True, "organizationId": organization2.id},
        ),
        status=200,
    )

    assert len(r["records"]) == 1


def test_edit_user_with_company_data(route_url, api, db_session, admin1):
    read_edit_user_url = route_url("user.read_edit", id=admin1.id)

    payload = {
        "first_name": "Larry",
        "last_name": "Potato",
        "email": "<EMAIL>",
        "phone_number": "A12345678",
        "photo_url": None,
        "details": {
            "description": "The Larry you all know",
        },
    }
    payload["details"]
    api(
        "patch",
        read_edit_user_url,
        payload,
        status=200,
    )
    r = api("get", read_edit_user_url, status=200)
    assert r["details"]["company"] is None

    payload["details"] = {"company_type": ""}
    api(
        "patch",
        read_edit_user_url,
        payload,
        status=200,
    )

    payload["details"] = {"company_type": CompanyType.LIMITED_COMPANY}
    api(
        "patch",
        read_edit_user_url,
        payload,
        status=400,
    )

    valid_company_payload = {
        "company_name": "company_name",
        "company_id": "company_id",
        "street_address": "street_address",
        "city": "city",
        "country": "country",
        "postal_code": "postal_code",
        "iban": "iban",
        "bic": "bic",
        "company_email": "company_email",
        "company_phone": "company_phone",
    }
    payload["details"]["company"] = valid_company_payload
    payload["details"]["company_type"] = CompanyType.LIMITED_COMPANY
    api(
        "patch",
        read_edit_user_url,
        payload,
        status=200,
    )

    event_log = db_session.scalars(
        select(EventLog).where(
            EventLog.object_type == EventLogObjectType.USER,
            EventLog.object_id == admin1.id,
            EventLog.action == EventLogAction.UPDATED,
        )
    ).all()

    assert len(event_log) == 3

    r = api("get", read_edit_user_url, status=200)
    assert list(r["details"]["company"].values()) == list(
        valid_company_payload.values()
    )


def test_edit_user(route_url, api, admin1):
    payload = {
        "first_name": "Larry",
        "last_name": "Potato",
        "email": "<EMAIL>",
        "phone_number": "A12345678",
        "photo_url": None,
        "details": {"description": "The Larry you all know"},
        "roaiib": "ROAIIB",
        "liabilityInsurance": "Liability Insurance",
    }
    r = api(
        "patch",
        route_url("user.read_edit", id=admin1.id),
        payload,
        status=200,
    )
    r = api("get", route_url("user.read_edit", id=admin1.id), status=200)
    assert r["firstName"] == payload["first_name"]
    assert r["email"] == payload["email"]
    assert r["phoneNumber"] == payload["phone_number"]
    assert r["roaiib"] == payload["roaiib"]
    assert r["liabilityInsurance"] == payload["liabilityInsurance"]
    assert r["details"]["description"] == payload["details"]["description"]

    payload2 = {"first_name": "Joaquin", "details": {"position": "Lead Realtor"}}
    r2 = api(
        "patch",
        route_url("user.read_edit", id=admin1.id),
        payload2,
        status=200,
    )
    r2 = api("get", route_url("user.read_edit", id=admin1.id), status=200)
    assert r2["firstName"] == payload2["first_name"]
    assert r2["lastName"] == payload["last_name"]
    assert r2["details"]["position"] == payload2["details"]["position"]
    assert r2["details"]["description"] == None


def test_edit_user_with_company_id(
    route_url, api, realtor1_organization2, admin1_organization2, company_1
):
    payload = {
        "company_id": company_1.id,
    }
    r = api(
        "patch",
        route_url("user.read_edit", id=realtor1_organization2.id),
        payload,
        role=admin1_organization2._roles[0],
        status=200,
    )
    r = api(
        "get", route_url("user.read_edit", id=realtor1_organization2.id), status=200
    )
    assert r["company"]["id"] == company_1.id
    assert r["company"]["name"] == company_1.name
    assert r["company"]["hasDiasApiKey"] == True


def test_edit_user_dias_api_key_by_fi_admin(
    route_url, db_session, api, realtor1_organization2, admin1_organization2
):
    # FI Admin can set dias api key for a user
    payload = {
        "dias_api_key": "Dias-Api-Key",
    }
    r = api(
        "patch",
        route_url("user.read_edit", id=realtor1_organization2.id),
        payload,
        role=admin1_organization2._roles[0],
        status=200,
    )
    r = api(
        "get", route_url("user.read_edit", id=realtor1_organization2.id), status=200
    )
    assert r["hasDiasApiKey"] == True
    db_session.refresh(realtor1_organization2)
    assert realtor1_organization2.has_dias_api_key == True
    assert realtor1_organization2.get_dias_api_key() == payload["dias_api_key"]

    # FI Admin can modify existing dias api key
    payload2 = {"dias_api_key": "Dias-Api-Key-2"}
    r2 = api(
        "patch",
        route_url("user.read_edit", id=realtor1_organization2.id),
        payload2,
        role=admin1_organization2._roles[0],
        status=200,
    )
    assert r2["hasDiasApiKey"] == True
    db_session.refresh(realtor1_organization2)
    assert realtor1_organization2.has_dias_api_key == True
    assert realtor1_organization2.get_dias_api_key() == payload2["dias_api_key"]

    # FI Admin can remove existing dias api key for a user
    payload3 = {"dias_api_key": ""}
    r3 = api(
        "patch",
        route_url("user.read_edit", id=realtor1_organization2.id),
        payload3,
        role=admin1_organization2._roles[0],
        status=200,
    )
    assert r3["hasDiasApiKey"] == False
    db_session.refresh(realtor1_organization2)
    assert realtor1_organization2.has_dias_api_key == False
    assert realtor1_organization2.get_dias_api_key() == None


def test_non_fi_admin_cannot_edit_user_dias_api_key(
    route_url, api, realtor1_organization2
):
    # Non-FI Admin cannot set dias api key for a user
    payload = {
        "dias_api_key": "Dias-Api-Key",
    }
    api(
        "patch",
        route_url("user.read_edit", id=realtor1_organization2.id),
        payload,
        role=realtor1_organization2._roles[0],
        status=403,
    )


def test_change_preview_mode(route_url, api, admin1):
    r = api(
        "patch",
        route_url("user.change_preview_mode", id=admin1.id),
        {"property_view_mode": "GRID", "property_open_mode": "FULL_VIEW"},
        status=200,
    )
    assert r["status"] == "ok"
    assert r["property_view_mode"] == "GRID"
    assert r["property_open_mode"] == "FULL_VIEW"

    r2 = api("get", route_url("user.read_edit", id=admin1.id), status=200)
    assert r2["details"]["propertyViewMode"] == "GRID"
    assert r2["details"]["propertyOpenMode"] == "FULL_VIEW"

    r3 = api(
        "patch",
        route_url("user.change_preview_mode", id=admin1.id),
        {"property_view_mode": "LIST"},
        status=200,
    )
    assert r3["property_view_mode"] == "LIST"


def test_edit_user_serviceform_booking_calendar_id_and_videobot_id(
    route_url, api, admin1
):
    payload = {
        "first_name": "Larry",
        "last_name": "Potato",
        "email": "<EMAIL>",
        "phone_number": "A12345678",
        "photo_url": None,
        "details": {
            "description": "The Larry you all know",
            "serviceform_booking_calendar_id": "1",
            "videobot_id": "1",
        },
    }
    r = api(
        "patch",
        route_url("user.read_edit", id=admin1.id),
        payload,
        status=200,
    )
    r = api("get", route_url("user.read_edit", id=admin1.id), status=200)
    print(r["details"])
    assert r["firstName"] == payload["first_name"]
    assert r["email"] == payload["email"]
    assert r["phoneNumber"] == payload["phone_number"]
    assert r["details"]["description"] == payload["details"]["description"]
    assert (
        r["details"]["serviceformBookingCalendarId"]
        == payload["details"]["serviceform_booking_calendar_id"]
    )
    assert r["details"]["videobotId"] == payload["details"]["videobot_id"]

    payload2 = {"first_name": "Joaquin", "details": {"position": "Lead Realtor"}}
    r2 = api(
        "patch",
        route_url("user.read_edit", id=admin1.id),
        payload2,
        status=200,
    )
    r2 = api("get", route_url("user.read_edit", id=admin1.id), status=200)
    assert r2["firstName"] == payload2["first_name"]
    assert r2["lastName"] == payload["last_name"]
    assert r2["details"]["position"] == payload2["details"]["position"]
    assert r2["details"]["description"] == None
    assert r2["details"]["serviceformBookingCalendarId"] == None
    assert r2["details"]["videobotId"] == None


# TODO uncomment when mocking emails is possible
# def test_create_user(route_url, api):
#     payload = {
#         "first_name": "Larry",
#         "last_name": "Potato",
#         "email": "<EMAIL>",
#         "phone_number": "A12345678",
#         "role": "Admin",
#     }
#     r = api(
#         "post",
#         route_url(
#             "user.list_create",
#         ),
#         payload,
#         status=201,
#     )
#     r = api("get", route_url("user.read_edit", id=r["id"]), status=200)
#     assert r["firstName"] == "Larry"
#     assert r["lastName"] == "Potato"
#     assert len(r["roles"]) == 1
#     assert r["roles"][0]["role"] == "Admin"


def test_activate_user(route_url, api, db_session, admin1, realtor1):
    # Make sure the user is deactivated
    realtor1.is_active = False

    r = api(
        "post",
        route_url("user.activate", id=realtor1.id),
        None,
        status=200,
        role=admin1._roles[0],
    )

    event_log = db_session.scalars(
        select(EventLog).where(
            EventLog.object_type == EventLogObjectType.USER,
            EventLog.object_id == realtor1.id,
            EventLog.action == EventLogAction.ACTIVATED,
        )
    ).one()

    assert event_log.data_before == {"is_active": False}
    assert event_log.data_after == {"is_active": True}

    r = api("get", route_url("user.read_edit", id=realtor1.id), status=200)

    assert r["isActive"] == True


def test_activate_user_without_permission(route_url, api, admin1, realtor1):
    api(
        "post",
        route_url("user.activate", id=admin1.id),
        None,
        status=403,
        role=realtor1._roles[0],
    )


def test_activate_already_activated_user(
    route_url,
    api,
    admin1,
):
    admin1.is_active = True

    api(
        "post",
        route_url("user.activate", id=admin1.id),
        None,
        status=400,
    )


def test_activate_non_existent_user(
    route_url,
    api,
):
    api(
        "post",
        route_url("user.activate", id="1231"),
        None,
        status=404,
    )


def test_user_activate_himself(route_url, api, admin1):
    api(
        "post",
        route_url("user.activate", id=admin1.id),
        None,
        status=400,
        role=admin1._roles[0],
    )


def test_deactivate_user(route_url, api, db_session, admin1, realtor1):
    # Make sure the user is activated
    realtor1.is_active = True

    r = api(
        "post",
        route_url("user.deactivate", id=realtor1.id),
        None,
        status=200,
        role=admin1._roles[0],
    )

    event_log = db_session.scalars(
        select(EventLog).where(
            EventLog.object_type == EventLogObjectType.USER,
            EventLog.object_id == realtor1.id,
            EventLog.action == EventLogAction.DEACTIVATED,
        )
    ).one()
    assert event_log.data_before == {"is_active": True}
    assert event_log.data_after == {"is_active": False}

    r = api("get", route_url("user.read_edit", id=realtor1.id), status=200)

    assert r["isActive"] == False


def test_deactivate_user_without_permission(route_url, api, admin1, realtor1):
    api(
        "post",
        route_url("user.deactivate", id=admin1.id),
        None,
        status=403,
        role=realtor1._roles[0],
    )


def test_deactivate_already_deactivated_user(
    route_url,
    api,
    realtor1,
):
    realtor1.is_active = False

    api(
        "post",
        route_url("user.deactivate", id=realtor1.id),
        None,
        status=400,
    )


def test_deactivate_non_existent_user(
    route_url,
    api,
):
    api(
        "post",
        route_url("user.deactivate", id="1231"),
        None,
        status=404,
    )


def test_user_deactivate_himself(route_url, api, admin1):
    api(
        "post",
        route_url("user.deactivate", id=admin1.id),
        None,
        status=400,
        role=admin1._roles[0],
    )


def test_change_user_role_single_organization(
    route_url, api, admin1, realtor1, organization1
):
    # Admin changes realtor's role to "Realtor" for a single organization
    r = api(
        "patch",
        route_url("user.change_role", id=realtor1.id),
        {"roles": [{"organizationId": organization1.id, "role": "Realtor"}]},
        status=200,
        role=admin1._roles[0],
    )

    # Fetch the user and verify the role has been updated
    r = api("get", route_url("user.read_edit", id=realtor1.id), status=200)

    assert len(r["roles"]) == 1
    assert r["roles"][0]["role"] == "Realtor"
    assert r["roles"][0]["organization"]["id"] == organization1.id
    assert r["roles"][0]["organization"]["name"] == organization1.name
    assert r["roles"][0]["organization"]["currency"] == organization1.currency
    assert r["roles"][0]["organization"]["language"] == organization1.language
    assert r["roles"][0]["organization"]["countryCode"] == organization1.country_code


def test_remove_user_role_from_organization(
    route_url, api, admin1, realtor1, organization1
):
    # Admin removes the realtor's role from a specific organization
    r = api(
        "patch",
        route_url("user.change_role", id=realtor1.id),
        {"roles": [{"organizationId": organization1.id, "role": None}]},
        status=200,
        role=admin1._roles[0],
    )

    # Fetch the user and verify the role has been removed
    r = api("get", route_url("user.read_edit", id=realtor1.id), status=200)

    assert not any(
        role["organization"]["id"] == organization1.id for role in r["roles"]
    )


def test_change_user_role_multiple_organizations(
    route_url, api, admin1, realtor1, organization1, organization2
):
    # Admin assigns roles to the realtor for multiple organizations
    r = api(
        "patch",
        route_url("user.change_role", id=realtor1.id),
        {
            "roles": [
                {"organizationId": organization1.id, "role": "Realtor"},
                {"organizationId": organization2.id, "role": "Photographer"},
            ]
        },
        status=200,
        role=admin1._roles[0],
    )

    # Fetch the user and verify the roles have been updated
    r = api("get", route_url("user.read_edit", id=realtor1.id), status=200)

    assert len(r["roles"]) == 2
    assert any(
        role["role"] == "Realtor" and role["organization"]["id"] == organization1.id
        for role in r["roles"]
    )
    assert any(
        role["role"] == "Photographer"
        and role["organization"]["id"] == organization2.id
        for role in r["roles"]
    )


def test_change_user_role_no_role_in_organization(
    route_url, api, admin1, realtor1, organization1, organization2
):
    # Admin assigns a role in one organization and removes it from another
    r = api(
        "patch",
        route_url("user.change_role", id=realtor1.id),
        {
            "roles": [
                {"organizationId": organization1.id, "role": "Photographer"},
                {"organizationId": organization2.id, "role": None},
            ]
        },
        status=200,
        role=admin1._roles[0],
    )

    # Fetch the user and verify the changes
    r = api("get", route_url("user.read_edit", id=realtor1.id), status=200)

    assert len(r["roles"]) == 1
    assert r["roles"][0]["role"] == "Photographer"
    assert r["roles"][0]["organization"]["id"] == organization1.id
    assert not any(
        role["organization"]["id"] == organization2.id for role in r["roles"]
    )


### New Test Cases for Error Handling


def test_change_user_role_invalid_role_name(
    route_url, api, admin1, realtor1, organization1
):
    # Admin tries to assign an invalid role name
    r = api(
        "patch",
        route_url("user.change_role", id=realtor1.id),
        {
            "roles": [{"organizationId": organization1.id, "role": "InvalidRole"}]
        },  # Invalid role
        status=400,
        role=admin1._roles[0],
    )

    # Check the response for an error
    assert "error" in r
    assert r["error"] == "Invalid user role information"


def test_change_user_role_invalid_organization_id(route_url, api, admin1, realtor1):
    # Admin tries to assign a role with an invalid organization ID
    r = api(
        "patch",
        route_url("user.change_role", id=realtor1.id),
        {
            "roles": [{"organizationId": 9999, "role": "Realtor"}]
        },  # Assuming 9999 is an invalid ID
        status=400,
        role=admin1._roles[0],
    )

    # Check the response for an error
    assert "error" in r
    assert r["error"] == "Could not update user roles"


def test_change_user_role_without_permission(route_url, api, admin1, realtor1):
    api(
        "patch",
        route_url("user.change_role", id=admin1.id),
        {"role": "Realtor"},
        status=403,
        role=realtor1._roles[0],
    )


def test_change_user_role_non_existent_user(route_url, api, admin1):
    api(
        "patch",
        route_url("user.change_role", id="xxx"),
        {"role": "Realtor"},
        status=404,
        role=admin1._roles[0],
    )


def test_change_user_role_unrelated_organization_not_affected(
    route_url, api, admin1, realtor1, organization1, organization2
):
    # Setup: Initially assign roles to the realtor in both organizations
    r = api(
        "patch",
        route_url("user.change_role", id=realtor1.id),
        {
            "roles": [
                {"organizationId": organization1.id, "role": "Realtor"},
                {"organizationId": organization2.id, "role": "Photographer"},
            ]
        },
        status=200,
        role=admin1._roles[0],
    )

    # Verify initial setup
    r = api("get", route_url("user.read_edit", id=realtor1.id), status=200)
    assert len(r["roles"]) == 2
    assert any(
        role["role"] == "Realtor" and role["organization"]["id"] == organization1.id
        for role in r["roles"]
    )
    assert any(
        role["role"] == "Photographer"
        and role["organization"]["id"] == organization2.id
        for role in r["roles"]
    )

    # Now, update role in organization1 only
    r = api(
        "patch",
        route_url("user.change_role", id=realtor1.id),
        {
            "roles": [
                {"organizationId": organization1.id, "role": "Photographer"},
            ]
        },
        status=200,
        role=admin1._roles[0],
    )

    # Fetch the user and verify that the role in organization1 is updated,
    # and the role in organization2 remains unchanged
    r = api("get", route_url("user.read_edit", id=realtor1.id), status=200)

    assert len(r["roles"]) == 2
    assert any(
        role["role"] == "Photographer"
        and role["organization"]["id"] == organization1.id
        for role in r["roles"]
    )
    assert any(
        role["role"] == "Photographer"
        and role["organization"]["id"] == organization2.id
        for role in r["roles"]
    )


def test_user_change_his_own_role(route_url, api, admin1):
    api(
        "patch",
        route_url("user.change_role", id=admin1.id),
        {"role": "Realtor"},
        status=400,
        role=admin1._roles[0],
    )


def test_list_realtors(
    route_url,
    api,
    realtor1,
    px8_fi_realtor,
    reaktor_realtor,
    loisto_realtor,
    reactron_realtor,
    normal_active_realtor,
    normal_unactive_realtor,
):
    # Missing X-Api-Key
    r = api(
        "get",
        route_url("user.list_realtors"),
        status=401,
    )

    r = api(
        "get",
        route_url("user.list_realtors"),
        headers={"X-Api-Key": app_cfg.strand_website_api_key},
        status=200,
    )
    assert r[0]["email"] == realtor1.email
    assert r[0]["status"] == "active"
    assert r[0]["office"] == ""
    assert r[0]["role"] == ""

    assert r[1]["email"] == normal_active_realtor.email
    assert r[1]["status"] == "active"
    assert r[1]["office"] == "Tomato Office, ES Office"
    assert r[1]["role"] == "Support Customer, Manager"

    assert r[2]["email"] == normal_unactive_realtor.email
    assert r[2]["status"] == "deactivated"
    assert r[2]["office"] == "Potato Office, Madrid Office"
    assert r[2]["role"] == "Devilery Manager, Support"
