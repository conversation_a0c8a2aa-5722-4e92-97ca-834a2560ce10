version: "3"

services:
  app:
    container_name: sp_app
    build:
      context: .
      dockerfile: Dockerfile.dev
    user: appuser
    volumes:
      - .:/app
    ports:
      - 6543:6543
    depends_on:
      - db
    env_file:
      - .env
    environment:
      DB_HOSTNAME: db
      DB_PORT: 3306
    command: while sleep 1000; do :; done
  adminer:
    image: adminer
    restart: always
    ports:
      - 8080:8080
  db:
    image: mariadb:10.11
    container_name: sp_db
    environment:
      MARIADB_USER: dev
      MARIADB_PASSWORD: dev
      MARIADB_DATABASE: dev_db
      MARIADB_ROOT_PASSWORD: root
    volumes:
      - ./data/docker_entrypoint:/docker-entrypoint-initdb.d
      - db-data:/var/lib/mysql
    ports:
      - 4407:3306
  ftp:
    image: delfer/alpine-ftp-server
    environment:
      USERS: ftpuser|ftppass
      ADDRESS: ftp.site.domain
    ports:
      - 21:21
      - 21000-21010:21000-21010
  localstack:
    image: localstack/localstack
    ports:
      - "127.0.0.1:4566:4566" # LocalStack Gateway
      - "127.0.0.1:4510-4559:4510-4559" # external services port range
    environment:
      - DEBUG=1
      - PERSISTENCE=1
      - AWS_DEFAULT_REGION=eu-west-1
    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./volume}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
  localstack-init:
    build:
      context: .
      dockerfile: Dockerfile.localstack-init
    depends_on:
      - localstack
    volumes:
      - ./scripts/initLocalStack.sh:/initLocalStack.sh
    environment:
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_DEFAULT_REGION=eu-west-1
    network_mode: "service:localstack"
volumes:
  db-data:
    driver: local
  localstack-data:
    driver: local
