name: CI/CD Pipeline

on:
  push:
    branches: [master, release/*]
    tags: ['v*.*.*']
  pull_request:
    branches: [master, develop]
  merge_group:
    branches: [master, release/*]

env:
  DB_USER: dev
  DB_PASS: dev
  DB_HOSTNAME: db
  DB_PORT: 3306
  DB_NAME: test_db
  AWS_ACCESS_KEY_ID: somecrets
  AWS_SECRET_ACCESS_KEY: somecrets
  JWT_SECRET: testing
  RESALE_ONLINE_CONTACT_ID: testing
  RESALE_ONLINE_API_KEY: testing
  STRAND_WEBSITE_API_KEY: potato
  STRAND_AI_API_KEY: something
  INTEGRATIONS_QUEUE_URL: someurl
  FOTOCASA_API_KEY: potato
  AWS_S3_FILES_BUCKET_NAME: somebucket
  STRAND_CLIENT_BASE_URL: https://stg.app.strand.es/
  SMTP_USER: pleasesetmeinprod
  SMTP_PASSWORD: pleasesetmeinprod
  IDEALISTA_CUSTOMER_CODE: potato
  IDEALISTA_FTP_SERVER: potato
  IDEALISTA_FTP_USER: potato
  IDEALISTA_FTP_PASSWORD: potato
  ALERT_EMAILS: '[]'
  INMOBILIEN_SCOUT_CLIENT_KEY: potato
  INMOBILIEN_SCOUT_CLIENT_SECRET: potato
  INMOBILIEN_SCOUT_RESOURCE_OWNER_KEY: potato
  INMOBILIEN_SCOUT_RESOURCE_OWNER_SECRET: potato
  INMOBILIEN_SCOUT_BASE_URL: potato
  RIGHT_MOVE_BASE_URL: https://adfapi.adftest.rightmove.com
  RIGHT_MOVE_NETWORK_ID: 00000
  RIGHT_MOVE_BRANCH_ID: 00000
  MS_ADMIN_ID: something
  MS_CLIENT_ID: something
  MS_CLIENT_SECRET: something
  MAILGUN_API_KEY: something
  MAILGUN_USER: potato
  MAILGUN_PASSWORD: potato
  API_REVISION: revision
  FACEBOOK_LEADS_ACCESS_CODE: something
  FERNET_KEY_STRING: nYJ2zpKkPChgCrHiWyRzlQRrZhJYSaQEV6o3cFGJjk8=
  COGNITO_REGION: eu-west-3
  COGNITO_USER_POOL_ID: userpoolid
  COGNITO_APP_CLIENT_ID: clientid

jobs:
  lint:
    name: Lint & Test
    runs-on: ubuntu-latest
    container: python:3.11.0
    timeout-minutes: 10
    services:
      db:
        image: mariadb:10.11
        env:
          MARIADB_USER: dev
          MARIADB_PASSWORD: dev
          MARIADB_DATABASE: test_db
          MARIADB_ROOT_PASSWORD: root

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.1
          virtualenvs-create: true
          virtualenvs-in-project: true
          installer-parallel: true

      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: venv-${{ runner.os }}-3.11.0-${{ hashFiles('**/poetry.lock') }}

      - name: Install dependencies
        if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
        run: poetry install

      - name: Check Alembic revisions
        if: github.event_name == 'merge_group'
        run: |
          branches_output=$(poetry run alembic branches)
          if [ -n "$branches_output" ]; then
            echo "Multiple Alembic revision branches detected:"
            echo "$branches_output"
            exit 1
          else
            echo "No multiple Alembic revision branches detected."
          fi

      - name: Run tests
        if: github.event_name != 'merge_group'
        run: poetry run pytest

  deploy:
    name: Deploy to AWS
    runs-on: ubuntu-latest
    needs: lint
    if: (github.event_name == 'push' && github.ref == 'refs/heads/master') || startsWith(github.ref, 'refs/tags/v')
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Determine Environment
        id: determine-env
        run: |
          echo "AWS_REGION=eu-west-3" >> $GITHUB_ENV
          echo "ECR_REPOSITORY=strand-backend" >> $GITHUB_ENV
          echo "ROLE_SESSION_NAME=GitHub_to_AWS_via_FederatedOIDC" >> $GITHUB_ENV
          echo "ROLE_NAME=GitHub_actions_deployment" >> $GITHUB_ENV

          if [[ "${{ github.ref }}" == "refs/heads/master" ]]; then
            echo "env=dev" >> $GITHUB_ENV
            echo "AWS_ACCOUNT=************" >> $GITHUB_ENV
          elif [[ "${{ github.ref }}" == refs/heads/release/* ]]; then
            echo "env=staging" >> $GITHUB_ENV
            echo "AWS_ACCOUNT=************" >> $GITHUB_ENV
          elif [[ "${{ github.ref }}" == refs/tags/v* ]]; then
            echo "env=production" >> $GITHUB_ENV
            echo "AWS_ACCOUNT=************" >> $GITHUB_ENV
          else
            echo "Unknown environment. Exiting."
            exit 1
          fi

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT }}:role/${{ env.ROLE_NAME }}
          role-session-name: ${{ env.ROLE_SESSION_NAME }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Verify AWS Caller Identity
        run: aws sts get-caller-identity

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and Push Docker Image
        env:
          IMAGE_TAG: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:latest
        run: |
          docker build -t $IMAGE_TAG .
          docker push $IMAGE_TAG

      - name: Fetch ECS Cluster and Service Names
        run: |
          echo "Fetching ECS Cluster and Service Names"

          ECS_CLUSTER=$(aws ecs list-clusters --query "clusterArns[?contains(@, 'strandproperties-backend')]" --output text)

          if [[ -z "$ECS_CLUSTER" ]]; then
            echo "ECS cluster not found for backend."
            exit 1
          fi

          ECS_BACKEND_SERVICE=$(aws ecs list-services --cluster "$ECS_CLUSTER" --query "serviceArns[?contains(@, 'strandproperties-backend-service')]" --output text)

          ECS_QUEUE_CONSUMER_SERVICE=$(aws ecs list-services --cluster "$ECS_CLUSTER" --query "serviceArns[?contains(@, 'strandproperties-queue-consumer-service')]" --output text)

          if [[ -z "$ECS_BACKEND_SERVICE" ]]; then
            echo "ECS backend service not found."
            exit 1
          fi

          if [[ -z "$ECS_QUEUE_CONSUMER_SERVICE" ]]; then
            echo "ECS queue consumer service not found."
            exit 1
          fi

          echo "AWS_ECS_CLUSTER_ID=$(echo "$ECS_CLUSTER" | sed 's|.*/||')" >> $GITHUB_ENV
          echo "AWS_ECS_BACKEND_SERVICE_NAME=$(echo "$ECS_BACKEND_SERVICE" | sed 's|.*/||')" >> $GITHUB_ENV
          echo "AWS_ECS_QUEUE_CONSUMER_SERVICE_NAME=$(echo "$ECS_QUEUE_CONSUMER_SERVICE" | sed 's|.*/||')" >> $GITHUB_ENV

      - name: Deploy backend to ECS
        run: |
          aws ecs update-service \
            --service "$AWS_ECS_BACKEND_SERVICE_NAME" \
            --cluster "$AWS_ECS_CLUSTER_ID" \
            --force-new-deployment

      - name: Deploy queue consumer to ECS
        run: |
          aws ecs update-service \
            --service "$AWS_ECS_QUEUE_CONSUMER_SERVICE_NAME" \
            --cluster "$AWS_ECS_CLUSTER_ID" \
            --force-new-deployment