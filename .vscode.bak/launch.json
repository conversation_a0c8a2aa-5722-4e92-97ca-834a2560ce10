{"$schema": "vscode://schemas/launch", "version": "0.2.0", "configurations": [{"name": "Backend: Debug sp_app (Run from repo root)", "type": "python", "request": "launch", "cwd": "${workspaceFolder}/backend", "program": "${workspaceFolder}/backend/dev_run.py", "console": "integratedTerminal", "envFile": "${workspaceFolder}/backend/.env", "env": {"DB_HOSTNAME": "localhost", "DB_PORT": "4407"}, "justMyCode": false, "stopOnEntry": false, "preLaunchTask": "Backend: Poetry Install"}, {"name": "Backend: PyTest (Run tests)", "type": "python", "request": "launch", "cwd": "${workspaceFolder}/backend", "module": "pytest", "args": ["-k", "::", "-s"], "console": "integratedTerminal", "envFile": "${workspaceFolder}/backend/.env", "env": {"DB_HOSTNAME": "localhost", "DB_PORT": "4407"}, "justMyCode": false, "preLaunchTask": "Backend: Poetry Install"}, {"name": "Backend: <PERSON><PERSON> (optional)", "type": "python", "request": "launch", "cwd": "${workspaceFolder}/backend", "module": "gunicorn", "args": ["--bind", "0.0.0.0:6543", "strandproperties.wsgi:application", "--timeout", "120"], "console": "integratedTerminal", "envFile": "${workspaceFolder}/backend/.env", "env": {"DB_HOSTNAME": "localhost", "DB_PORT": "4407"}, "justMyCode": false, "preLaunchTask": "Backend: Poetry Install"}]}