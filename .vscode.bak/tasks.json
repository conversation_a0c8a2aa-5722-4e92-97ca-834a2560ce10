{"version": "2.0.0", "tasks": [{"label": "Backend: Poetry Install", "type": "shell", "options": {"cwd": "${workspaceFolder}/backend", "env": {"POETRY_VIRTUALENVS_IN_PROJECT": "true"}}, "command": "poetry env use python3.11 && poetry install", "problemMatcher": [], "group": "build"}, {"label": "Backend: DB Mi<PERSON>", "type": "shell", "options": {"cwd": "${workspaceFolder}/backend"}, "command": "poetry run alembic upgrade head", "problemMatcher": [], "dependsOn": ["Backend: Poetry Install"]}, {"label": "Backend: Seed Dev Data", "type": "shell", "options": {"cwd": "${workspaceFolder}/backend"}, "command": "poetry run seed --sowise-dev", "problemMatcher": [], "dependsOn": ["Backend: DB Mi<PERSON>"]}, {"label": "Backend: Start <PERSON> via <PERSON><PERSON>", "type": "shell", "options": {"cwd": "${workspaceFolder}/backend"}, "command": "docker compose -f docker-compose.local.yml up -d db", "problemMatcher": [], "group": "build"}, {"label": "Backend: Stop DB via <PERSON>er", "type": "shell", "options": {"cwd": "${workspaceFolder}/backend"}, "command": "docker compose -f docker-compose.local.yml stop db", "problemMatcher": []}]}