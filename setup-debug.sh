#!/bin/bash

# Setup script for debugging SP App locally on macOS
set -e

echo "🚀 Setting up SP App for local debugging..."

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ This script is designed for macOS"
    exit 1
fi

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please install Poetry first:"
    echo "curl -sSL https://install.python-poetry.org | python3 -"
    exit 1
fi

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker Desktop first."
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker Desktop."
    exit 1
fi

# Install WeasyPrint dependencies for macOS
echo "📦 Installing WeasyPrint dependencies..."
if command -v brew &> /dev/null; then
    brew install pango cairo gdk-pixbuf libffi
else
    echo "⚠️  Homebrew not found. Please install WeasyPrint dependencies manually:"
    echo "brew install pango cairo gdk-pixbuf libffi"
fi

# Navigate to backend directory
cd backend

# Install Python dependencies
echo "📦 Installing Python dependencies..."
poetry install

# Start dependencies (database, localstack, etc.)
echo "🐳 Starting Docker dependencies..."
docker compose -f docker-compose.local.yml up -d db localstack localstack-init ftp

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Run database migrations
echo "🗄️  Running database migrations..."
poetry run alembic upgrade head

# Seed database
echo "🌱 Seeding database..."
poetry run seed --sowise-dev

echo "✅ Setup complete!"
echo ""
echo "🎯 To start debugging:"
echo "1. Open VSCode in the project root directory"
echo "2. Go to Run and Debug (Cmd+Shift+D)"
echo "3. Select 'Debug SP App' configuration"
echo "4. Press F5 or click the play button"
echo ""
echo "🌐 The app will be available at: http://localhost:6543"
echo "📊 API explorer at: http://localhost:6543/-/spec"
echo "🗄️  Database admin at: http://localhost:8080"
echo ""
echo "💡 To create a test user:"
echo "cd backend && poetry <NAME_EMAIL> pwd --first-name=John --last-name=Doe --role=Admin --organization=1"
