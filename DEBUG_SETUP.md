# Debug Setup cho SP App trên VSCode

Hướng dẫn này giúp bạn setup debug cho SP App trực tiếp trên VSCode thay vì chạy trong container.

## Yêu cầu

- macOS
- Python 3.11+
- Poetry 2.1+
- Docker Desktop
- VSCode với Python extension

## Setup nhanh

Chạy script setup tự động:

```bash
./setup-debug.sh
```

## Setup thủ công

### 1. Cài đặt dependencies cho WeasyPrint

```bash
brew install pango cairo gdk-pixbuf libffi
```

### 2. Cài đặt Python dependencies

```bash
cd backend
poetry install
```

### 3. Khởi động các services phụ thuộc

```bash
docker compose -f backend/docker-compose.local.yml up -d db localstack localstack-init ftp
```

### 4. Chạy migrations và seed data

```bash
cd backend
poetry run alembic upgrade head
poetry run seed --sowise-dev
```

### 5. Tạo user test

```bash
cd backend
poetry <NAME_EMAIL> pwd --first-name=John --last-name=Doe --role=Admin --organization=1
```

## Debug trong VSCode

### Cấu hình có sẵn

Tôi đã tạo 3 cấu hình debug trong `.vscode/launch.json`:

1. **Debug SP App** - Chạy với dev_run.py và tự động start dependencies
2. **Debug SP App (Simple)** - Chạy đơn giản không auto-start dependencies  
3. **Debug SP App with Gunicorn** - Chạy với Gunicorn như production

### Cách debug

1. Mở VSCode tại thư mục root (`/MyStrands/`)
2. Nhấn `Cmd+Shift+D` để mở Run and Debug
3. Chọn cấu hình "Debug SP App"
4. Nhấn `F5` hoặc click nút play

### Breakpoints

- Đặt breakpoints bằng cách click vào số dòng trong editor
- Breakpoints sẽ hoạt động trong tất cả code Python
- Có thể debug step-by-step với F10 (step over), F11 (step into)

## Tasks có sẵn

Trong `.vscode/tasks.json` có các tasks hữu ích:

- **start-dependencies**: Khởi động DB, LocalStack, FTP
- **stop-dependencies**: Dừng tất cả dependencies
- **install-dependencies**: Cài đặt Python packages
- **run-migrations**: Chạy database migrations
- **seed-database**: Seed dữ liệu test
- **setup-dev-environment**: Chạy tất cả setup cần thiết

Chạy tasks: `Cmd+Shift+P` → "Tasks: Run Task"

## URLs quan trọng

- **App**: http://localhost:6543
- **API Explorer**: http://localhost:6543/-/spec  
- **Database Admin**: http://localhost:8080
- **LocalStack**: http://localhost:4566

## Troubleshooting

### Lỗi kết nối database

Kiểm tra container database đang chạy:
```bash
docker ps | grep sp_db
```

### Lỗi import modules

Kiểm tra PYTHONPATH trong launch.json đã đúng chưa.

### Lỗi WeasyPrint

Cài đặt lại dependencies:
```bash
brew reinstall pango cairo gdk-pixbuf libffi
```

### Port đã được sử dụng

Kiểm tra process nào đang dùng port 6543:
```bash
lsof -i :6543
```

## So sánh với Container

| Aspect | Container | Local Debug |
|--------|-----------|-------------|
| Setup | Đơn giản | Phức tạp hơn |
| Debug | Khó | Dễ dàng |
| Performance | Chậm hơn | Nhanh hơn |
| Hot reload | Có | Có |
| Breakpoints | Khó setup | Dễ dàng |

## Lưu ý

- Database vẫn chạy trong container (port 4407)
- LocalStack chạy trong container cho AWS services
- Chỉ SP App chạy local để debug
- File `.env` trong backend/ sẽ được load tự động
